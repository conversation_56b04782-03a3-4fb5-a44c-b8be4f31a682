{"openapi": "3.0.3", "info": {"title": "TrendInsight API", "description": "巨量引擎平台数据搜索API - 基于Gofly框架的企业级微服务接口", "version": "1.0.0", "contact": {"name": "API Support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "http://localhost:8200", "description": "开发环境"}, {"url": "https://api.example.com", "description": "生产环境"}], "security": [{"BearerAuth": []}], "tags": [{"name": "TrendInsight Index", "description": "基础搜索功能 - 视频和创作者搜索"}, {"name": "TrendInsight Config", "description": "配置管理 - 配置查看、连接测试和API信息"}, {"name": "关键词监控管理", "description": "关键词监控管理 - 用户关键词监控列表、个性化配置和统计信息"}, {"name": "关键词管理", "description": "关键词管理 - 关键词创建、更新和同步到 MediaCrawler 服务"}, {"name": "抖音数据同步", "description": "抖音数据同步管理 - 收藏夹同步、同步记录查询和统计信息"}], "paths": {"/client/trendinsight/searchVideos": {"post": {"tags": ["TrendInsight Index"], "summary": "搜索巨量引擎平台视频", "description": "根据关键词和筛选条件搜索巨量引擎平台的视频内容，支持多维度搜索参数", "operationId": "searchVideos", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoSearchRequest"}, "examples": {"basic_search": {"summary": "基础搜索示例", "value": {"keyword": "人工智能"}}, "video_search": {"summary": "视频搜索示例", "value": {"keyword": "人工智能", "type": "video"}}, "author_search": {"summary": "作者搜索示例", "value": {"keyword": "人工智能", "type": "author"}}, "advanced_search": {"summary": "高级搜索示例", "value": {"keyword": "人工智能", "type": "video", "author_ids": ["author1", "author2"], "category_id": "1", "date_type": 1, "label_type": 1, "duration_type": 2}}}}}}, "responses": {"200": {"description": "搜索成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoSearchResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "权限不足", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "参数验证失败", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/client/trendinsight/searchAuthors": {"post": {"tags": ["TrendInsight Index"], "summary": "搜索巨量引擎平台创作者", "description": "根据关键词搜索巨量引擎平台的创作者/达人信息", "operationId": "searchAuthors", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatorSearchRequest"}, "examples": {"basic_search": {"summary": "基础搜索示例", "value": {"keyword": "科技博主"}}, "author_search": {"summary": "作者搜索示例", "value": {"keyword": "科技博主", "type": "author"}}, "video_search": {"summary": "视频搜索示例", "value": {"keyword": "科技博主", "type": "video"}}, "limited_search": {"summary": "限制数量搜索示例", "value": {"keyword": "科技博主", "type": "author", "total": 20}}}}}}, "responses": {"200": {"description": "搜索成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthorSearchResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "权限不足", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "422": {"description": "参数验证失败", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/client/trendinsight/getUserVideoKeywords": {"get": {"tags": ["关键词监控管理"], "summary": "获取用户视频关键词监控列表", "description": "分页获取当前用户的视频关键词监控列表，包含用户个性化配置和关键词详细信息", "operationId": "getUserVideoKeywords", "security": [{"BearerAuth": []}], "parameters": [{"name": "page", "in": "query", "required": false, "description": "页码，从1开始", "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "page_size", "in": "query", "required": false, "description": "每页数量", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetUserKeywordListResponse"}}}}, "401": {"description": "用户未登录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/client/trendinsight/getUserAuthorKeywords": {"get": {"tags": ["关键词监控管理"], "summary": "获取用户作者关键词监控列表", "description": "分页获取当前用户的作者关键词监控列表，包含用户个性化配置和关键词详细信息", "operationId": "getUserAuthorKeywords", "security": [{"BearerAuth": []}], "parameters": [{"name": "page", "in": "query", "required": false, "description": "页码，从1开始", "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "page_size", "in": "query", "required": false, "description": "每页数量", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetUserAuthorKeywordListResponse"}}}}, "401": {"description": "用户未登录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/client/trendinsight/delUserKeywordParId/{id}": {"delete": {"tags": ["关键词监控管理"], "summary": "删除用户关键词关联", "description": "删除用户的关键词监控关联，执行软删除操作", "operationId": "delUserKeywordParId", "security": [{"BearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "关键词关联的UUID", "schema": {"type": "string", "format": "uuid", "pattern": "^[a-f0-9]{32}$", "example": "1234567890abcdef1234567890abcdef"}}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteUserKeywordResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "用户未登录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "权限不足", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "关键词不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/client/trendinsight/upsertVideoKeywords": {"post": {"tags": ["关键词管理"], "summary": "创建或更新视频关键词", "description": "创建或更新单个视频关键词到 MediaCrawler 服务，并建立用户关键词关联。操作流程：1) 调用 MediaCrawler 服务创建/更新关键词；2) 在本地数据库创建用户关键词关联记录。", "operationId": "upsertVideoKeywords", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertVideoKeywordRequest"}, "examples": {"video_keyword": {"summary": "视频关键词示例", "value": {"keyword": "人工智能"}}}}}}, "responses": {"200": {"description": "视频关键词创建/更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertVideoKeywordResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/client/trendinsight/upsertAuthorKeywords": {"post": {"tags": ["关键词管理"], "summary": "创建或更新作者关键词", "description": "通过单个用户ID创建或更新作者关键词到 MediaCrawler 服务，并建立用户关键词关联。操作流程：1) 调用 MediaCrawler 服务创建/更新作者信息；2) 在本地数据库创建用户关键词关联记录。", "operationId": "upsertAuthorKeywords", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertAuthorKeywordRequest"}, "examples": {"author_keyword": {"summary": "作者关键词示例", "value": {"user_id": "douyin_123456"}}}}}}, "responses": {"200": {"description": "作者关键词创建/更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertAuthorKeywordResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/client/trendinsight/getVideoKeywordStats": {"get": {"tags": ["关键词统计"], "summary": "获取用户视频关键词统计信息", "description": "获取当前用户的视频关键词统计信息，包括每个关键词关联的视频总数和今日新增视频数", "operationId": "getVideoKeywordStats", "security": [{"BearerAuth": []}], "parameters": [], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoKeywordStatsResponse"}}}}, "401": {"description": "用户未登录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/client/trendinsight/getAuthorKeywordStats": {"get": {"tags": ["关键词统计"], "summary": "获取用户作者关键词统计信息", "description": "获取当前用户的作者关键词统计信息，包括每个关键词关联的作者总数和今日新增作者数", "operationId": "getAuthorKeywordStats", "security": [{"BearerAuth": []}], "parameters": [], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthorKeywordStatsResponse"}}}}, "401": {"description": "用户未登录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/client/trendinsight/config/getConfig": {"get": {"tags": ["TrendInsight Config"], "summary": "获取TrendInsight配置信息", "description": "获取当前TrendInsight服务的配置信息，包括基础URL、超时设置、重试次数和API参数说明", "operationId": "getConfig", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "配置信息获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigResponse"}}}}, "401": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "权限不足", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/client/trendinsight/config/testConnection": {"post": {"tags": ["TrendInsight Config"], "summary": "测试TrendInsight连接", "description": "测试与MediaCrawler服务的连接状态，验证配置是否正确", "operationId": "testConnection", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "连接测试完成", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConnectionTestResponse"}}}}, "401": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "权限不足", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/client/trendinsight/config/getApiInfo": {"get": {"tags": ["TrendInsight Config"], "summary": "获取API信息", "description": "获取TrendInsight API的详细信息，包括版本、端点列表和参数说明", "operationId": "getApiInfo", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "API信息获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiInfoResponse"}}}}, "401": {"description": "未授权访问", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "权限不足", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/client/user/douyin/getSyncRecords": {"get": {"tags": ["抖音数据同步"], "summary": "获取抖音同步记录", "description": "获取用户的抖音同步记录列表，支持分页查询", "operationId": "getSyncRecords", "security": [{"BearerAuth": []}], "parameters": [{"name": "page", "in": "query", "required": false, "description": "页码，从1开始", "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "page_size", "in": "query", "required": false, "description": "每页记录数，最大100", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20, "example": 20}}], "responses": {"200": {"description": "获取同步记录成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetSyncRecordsResponse"}}}}, "401": {"description": "用户未登录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/client/user/douyin/getSyncStats": {"get": {"tags": ["抖音数据同步"], "summary": "获取用户视频统计信息", "description": "获取用户的视频统计信息，基于 user_inbox_video_related 表统计，包括总记录数、按来源类型分组统计和最新记录时间", "operationId": "getSyncStats", "security": [{"BearerAuth": []}], "parameters": [{"name": "days", "in": "query", "required": false, "description": "统计天数参数（保留兼容性，当前版本基于实际记录统计）", "schema": {"type": "integer", "minimum": 1, "maximum": 365, "default": 30, "example": 30}}], "responses": {"200": {"description": "获取视频统计成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetUserVideoStatsResponse"}}}}, "401": {"description": "用户未登录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/client/user/douyin/getSyncRecordRelated": {"get": {"tags": ["抖音数据同步"], "summary": "获取同步记录关联视频", "description": "获取指定同步记录的关联视频列表，支持分页查询", "operationId": "getSyncRecordRelated", "security": [{"BearerAuth": []}], "parameters": [{"name": "sync_record_uuid", "in": "query", "required": true, "description": "同步记录UUID", "schema": {"type": "string", "example": "1234567890abcdef1234567890abcdef"}}, {"name": "page", "in": "query", "required": false, "description": "页码，从1开始", "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "page_size", "in": "query", "required": false, "description": "每页记录数，最大100", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20, "example": 20}}], "responses": {"200": {"description": "获取同步记录关联成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetSyncRecordRelatedResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "用户未登录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "同步记录不存在或不属于当前用户", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/client/user/douyin/getSyncRecordRelatedList": {"get": {"tags": ["抖音数据同步"], "summary": "获取用户所有同步记录的关联视频列表", "description": "获取当前用户所有同步记录的关联视频列表，支持分页查询，包含完整的抖音视频详细信息", "operationId": "getSyncRecordRelatedList", "security": [{"BearerAuth": []}], "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "page_size", "in": "query", "description": "每页记录数", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20, "example": 20}}], "responses": {"200": {"description": "获取同步记录关联成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetSyncRecordRelatedListResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "用户未登录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/client/user/douyin/getVideoDetail": {"get": {"tags": ["抖音数据同步"], "summary": "获取抖音视频详情", "description": "通过aweme_id获取抖音视频的详细信息，包括视频元数据、统计数据和作者信息", "operationId": "getVideoDetail", "security": [{"BearerAuth": []}], "parameters": [{"name": "aweme_id", "in": "query", "required": true, "description": "抖音视频ID", "schema": {"type": "string", "minLength": 10, "example": "7123456789012345678"}}], "responses": {"200": {"description": "获取视频详情成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetVideoDetailResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"invalid_aweme_id": {"summary": "aweme_id参数错误", "value": {"code": 400, "msg": "aweme_id参数不能为空", "data": null}}, "format_error": {"summary": "aweme_id格式错误", "value": {"code": 400, "msg": "aweme_id格式不正确", "data": null}}}}}}, "404": {"description": "视频不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"not_found": {"summary": "视频不存在", "value": {"code": 404, "msg": "视频不存在", "data": null}}, "deleted": {"summary": "视频已被删除", "value": {"code": 404, "msg": "视频信息获取失败，可能是视频不存在或已被删除", "data": null}}}}}}, "403": {"description": "访问被拒绝", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"access_denied": {"summary": "访问被拒绝", "value": {"code": 403, "msg": "访问被拒绝，可能需要更新Cookie", "data": null}}}}}}, "429": {"description": "请求过于频繁", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"rate_limit": {"summary": "请求频率限制", "value": {"code": 429, "msg": "请求过于频繁，请稍后重试", "data": null}}}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"server_error": {"summary": "服务器错误", "value": {"code": 500, "msg": "服务器内部错误，请稍后重试", "data": null}}, "service_unavailable": {"summary": "服务不可用", "value": {"code": 500, "msg": "服务暂时不可用，请稍后重试", "data": null}}}}}}}}}, "/client/user/douyin/syncCollects": {"post": {"tags": ["抖音数据同步"], "summary": "同步抖音收藏夹", "description": "同步用户的抖音收藏夹数据，包括收藏夹信息和视频信息", "operationId": "syncCollects", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "同步成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncCollectsResponse"}}}}, "400": {"description": "请求参数错误或用户信息不完整", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "用户未登录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/client/user/douyin/bindDouyinCookie": {"post": {"tags": ["抖音数据同步"], "summary": "绑定抖音<PERSON>ie", "description": "绑定用户的抖音Cookie，用于后续的数据同步操作", "operationId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BindDouyinCookieRequest"}}}}, "responses": {"200": {"description": "Cookie绑定成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BindDouyinCookieResponse"}}}}, "400": {"description": "请求参数错误或Cookie无效", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "用户未登录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/client/user/douyin/getVideoRelatedList": {"get": {"tags": ["抖音数据同步"], "summary": "获取用户收件箱视频关联记录列表", "description": "查询当前用户的收件箱视频关联记录列表，支持过滤、排序和分页", "operationId": "getVideoRelatedList", "parameters": [{"name": "source_type", "in": "query", "description": "来源类型过滤，支持：video（关键词）、author（作者）、collect（收藏夹）。为保持向后兼容，同时支持 KEYWORD、AUTHOR、COLLECT", "required": false, "schema": {"type": "string", "enum": ["video", "author", "collect", "KEYWORD", "AUTHOR", "COLLECT"], "example": "collect"}}, {"name": "sort_by", "in": "query", "description": "排序字段，支持：create_time（添加时间）、publish_time（发布时间）", "required": false, "schema": {"type": "string", "enum": ["create_time", "publish_time"], "default": "create_time", "example": "create_time"}}, {"name": "sort_order", "in": "query", "description": "排序方向，支持：asc（升序）、desc（降序）", "required": false, "schema": {"type": "string", "enum": ["asc", "desc"], "default": "desc", "example": "desc"}}, {"name": "page", "in": "query", "description": "页码，从1开始", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "page_size", "in": "query", "description": "每页数量，范围：1-100", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20, "example": 20}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoRelatedListResponse"}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "用户未登录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"BearerAuth": []}]}}, "/client/user/douyin/getVideoRelatedStats": {"get": {"tags": ["抖音数据同步"], "summary": "获取用户收件箱视频关联记录统计信息", "description": "获取当前用户在 user_inbox_video_related 表中的视频关联记录统计信息", "operationId": "getVideoRelatedStats", "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoRelatedStatsResponse"}}}}, "401": {"description": "用户未登录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"BearerAuth": []}]}}, "/client/user/douyin/addVideoToAssets": {"post": {"tags": ["抖音数据同步"], "summary": "添加视频到素材库", "description": "将 user_inbox_video_related 表中状态为 PENDING 的视频数据添加到 assets 表中，支持从收藏夹、关键词、作者等来源添加视频素材", "operationId": "addVideoToAssets", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddVideoToAssetsRequest"}}}}, "responses": {"200": {"description": "添加成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddVideoToAssetsResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"empty_uuid": {"summary": "记录UUID参数为空", "value": {"code": 400, "msg": "记录UUID参数不能为空", "data": {}}}, "record_not_found": {"summary": "记录不存在", "value": {"code": 400, "msg": "视频记录不存在或无权限访问", "data": {}}}, "invalid_status": {"summary": "记录状态不正确", "value": {"code": 400, "msg": "视频记录状态为 SUCCESS，只能处理状态为 PENDING 的记录", "data": {}}}, "video_not_found": {"summary": "视频信息不存在", "value": {"code": 400, "msg": "视频详细信息不存在，请先同步视频数据", "data": {}}}, "already_exists": {"summary": "视频已存在", "value": {"code": 400, "msg": "该视频已经添加到素材库中", "data": {}}}}}}}, "401": {"description": "用户未登录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "security": [{"BearerAuth": []}]}}, "/client/user/douyin/getAuthorVideos": {"get": {"tags": ["抖音数据查询"], "summary": "根据趋势洞察用户ID查询作者发布的视频", "description": "根据trendinsight_user_id和user_uuid查询user_inbox_video_related表获取数据，然后关联查询douyin_aweme表获取视频详细信息。支持分页查询，按发布时间倒序排列。适用于查询用户收件箱中特定作者的视频数据。", "operationId": "getAuthorVideos", "parameters": [{"name": "trendinsight_user_id", "in": "query", "required": true, "description": "趋势洞察用户ID，用于标识特定的抖音作者，通常以MS4wLjABAAAA开头的字符串", "schema": {"type": "string", "minLength": 5, "maxLength": 64, "example": "MS4wLjABAAAA1234567890abcdef"}}, {"name": "page", "in": "query", "required": false, "description": "页码，从1开始，用于分页查询。当数据量较大时建议使用分页", "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "page_size", "in": "query", "required": false, "description": "每页数量，范围1-100，控制单次返回的视频数量。建议根据实际需求调整，避免单次请求数据过多", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20, "example": 20}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "msg": {"type": "string", "example": "查询作者视频成功"}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/UserInboxVideoRelatedWithAweme"}, "description": "用户收件箱视频关联记录列表，包含视频关联信息和抖音视频详细信息"}, "total": {"type": "integer", "description": "总视频数量"}, "page": {"type": "integer", "description": "当前页码"}, "page_size": {"type": "integer", "description": "每页数量"}, "total_pages": {"type": "integer", "description": "总页数"}}}}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"missing_user_id": {"summary": "趋势洞察用户ID参数不能为空", "value": {"code": 400, "msg": "趋势洞察用户ID参数不能为空", "data": {}}}, "invalid_user_id_format": {"summary": "趋势洞察用户ID格式不正确", "value": {"code": 400, "msg": "趋势洞察用户ID格式不正确", "data": {}}}, "invalid_page": {"summary": "页码参数无效", "value": {"code": 400, "msg": "页码参数无效，必须大于0", "data": {}}}, "invalid_page_size": {"summary": "每页数量参数无效", "value": {"code": 400, "msg": "每页数量参数无效，范围：1-100", "data": {}}}}}}}, "401": {"description": "用户未登录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"unauthorized": {"summary": "用户未登录", "value": {"code": 401, "msg": "用户未登录，请先进行身份认证", "data": {}}}}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"database_error": {"summary": "查询数据库失败", "value": {"code": 500, "msg": "服务器内部错误，查询数据库失败", "data": {}}}}}}}}, "security": [{"BearerAuth": []}]}}}, "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT Bearer Token认证，格式：Bearer <token>"}}, "schemas": {"VideoSearchRequest": {"type": "object", "required": ["keyword"], "properties": {"keyword": {"type": "string", "description": "搜索关键词", "minLength": 1, "maxLength": 100, "example": "人工智能"}, "type": {"type": "string", "description": "搜索类型：video-视频搜索，author-作者搜索", "enum": ["video", "author"], "default": "video", "example": "video"}, "author_ids": {"type": "array", "description": "作者ID列表，可选参数", "items": {"type": "string"}, "example": ["author1", "author2"]}, "category_id": {"type": "string", "description": "分类ID，可选参数", "example": "1"}, "date_type": {"type": "integer", "description": "日期类型：0-全部时间，1-最近7天，2-最近30天，3-最近90天", "minimum": 0, "maximum": 3, "example": 1}, "label_type": {"type": "integer", "description": "标签类型：0-全部标签，1-原创，2-转发", "minimum": 0, "maximum": 2, "example": 1}, "duration_type": {"type": "integer", "description": "时长类型：0-全部时长，1-短视频，2-中等时长，3-长视频", "minimum": 0, "maximum": 3, "example": 2}}}, "CreatorSearchRequest": {"type": "object", "required": ["keyword"], "properties": {"keyword": {"type": "string", "description": "搜索关键词", "minLength": 1, "maxLength": 100, "example": "科技博主"}, "type": {"type": "string", "description": "搜索类型：author-作者搜索，video-视频搜索", "enum": ["author", "video"], "default": "author", "example": "author"}, "total": {"type": "integer", "description": "返回数量，可选参数", "minimum": 1, "maximum": 100, "example": 20}}}, "VideoSearchResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "视频搜索成功"}, "data": {"$ref": "#/components/schemas/TrendInsightVideoSearchResponseSchema"}}}, "AuthorSearchResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "创作者搜索成功"}, "data": {"$ref": "#/components/schemas/TrendInsightAuthorSearchResponseSchema"}}}, "ConfigResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "配置信息获取成功"}, "data": {"type": "object", "properties": {"base_url": {"type": "string", "description": "MediaCrawler基础URL", "example": "http://localhost:8000"}, "timeout": {"type": "string", "description": "请求超时时间", "example": "30s"}, "retry_count": {"type": "integer", "description": "重试次数", "example": 3}, "endpoints": {"type": "object", "description": "API端点配置", "properties": {"search_videos": {"type": "string", "example": "/api/v1/trendinsight/search/videos"}, "search_creators": {"type": "string", "example": "/api/v1/trendinsight/search/creators"}}}, "parameters": {"type": "object", "description": "参数配置说明", "properties": {"video_search": {"type": "object", "properties": {"required": {"type": "array", "items": {"type": "string"}, "example": ["keyword"]}, "optional": {"type": "array", "items": {"type": "string"}, "example": ["type", "author_ids", "category_id", "date_type", "label_type", "duration_type"]}, "date_types": {"type": "object", "additionalProperties": {"type": "string"}, "example": {"0": "全部时间", "1": "最近7天", "2": "最近30天", "3": "最近90天"}}, "label_types": {"type": "object", "additionalProperties": {"type": "string"}, "example": {"0": "全部标签", "1": "原创", "2": "转发"}}, "duration_types": {"type": "object", "additionalProperties": {"type": "string"}, "example": {"0": "全部时长", "1": "短视频", "2": "中等时长", "3": "长视频"}}}}, "creator_search": {"type": "object", "properties": {"required": {"type": "array", "items": {"type": "string"}, "example": ["keyword"]}, "optional": {"type": "array", "items": {"type": "string"}, "example": ["type", "total"]}, "limits": {"type": "object", "properties": {"total_min": {"type": "integer", "example": 1}, "total_max": {"type": "integer", "example": 100}}}}}}}}}}}, "ConnectionTestResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "连接测试成功"}, "data": {"type": "object", "properties": {"connected": {"type": "boolean", "description": "连接状态", "example": true}, "base_url": {"type": "string", "description": "测试的基础URL", "example": "http://localhost:8000"}, "status_code": {"type": "integer", "description": "HTTP状态码", "example": 200}, "response_time": {"type": "string", "description": "响应时间", "example": "< 1s"}, "test_time": {"type": "string", "description": "测试时间", "example": "2024-06-19 15:30:45"}, "error": {"type": "string", "description": "错误信息（连接失败时）", "example": "connection refused"}}}}}, "ApiInfoResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "API信息获取成功"}, "data": {"type": "object", "properties": {"service_name": {"type": "string", "description": "服务名称", "example": "TrendInsight API"}, "version": {"type": "string", "description": "API版本", "example": "v1.0.0"}, "description": {"type": "string", "description": "服务描述", "example": "巨量引擎平台数据搜索API"}, "endpoints": {"type": "array", "description": "端点列表", "items": {"type": "object", "properties": {"path": {"type": "string", "description": "端点路径"}, "method": {"type": "string", "description": "HTTP方法"}, "description": {"type": "string", "description": "端点描述"}, "parameters": {"type": "object", "description": "参数说明", "additionalProperties": {"type": "string"}}}}}, "error_codes": {"type": "object", "description": "错误码说明", "additionalProperties": {"type": "string"}}}}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误状态码", "example": 400}, "msg": {"type": "string", "description": "错误消息", "example": "请求参数错误"}, "data": {"type": "object", "description": "错误详细信息", "nullable": true}}}, "ValidationErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "验证错误状态码", "example": 422}, "msg": {"type": "string", "description": "验证错误消息", "example": "参数验证失败"}, "data": {"type": "object", "description": "验证错误详情", "properties": {"detail": {"type": "array", "description": "详细错误信息", "items": {"type": "object", "properties": {"loc": {"type": "array", "description": "错误位置", "items": {"oneOf": [{"type": "string"}, {"type": "integer"}]}}, "msg": {"type": "string", "description": "错误消息"}, "type": {"type": "string", "description": "错误类型"}}}}}}}}, "GetTaskListResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "获取任务列表成功"}, "data": {"type": "object", "properties": {"tasks": {"type": "array", "description": "任务列表", "items": {"$ref": "#/components/schemas/TaskListItem"}}, "total": {"type": "integer", "description": "总任务数量", "example": 25}, "page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页数量", "example": 10}, "total_pages": {"type": "integer", "description": "总页数", "example": 3}, "has_more": {"type": "boolean", "description": "是否有更多数据", "example": true}}}}}, "TaskListItem": {"type": "object", "description": "增强的任务列表项，包含用户关键词个性化信息", "properties": {"task_id": {"type": "string", "description": "任务ID", "example": "douyin_1640995200_12345678"}, "task_name": {"type": "string", "description": "任务名称", "example": "douyin_video_人工智能"}, "description": {"type": "string", "description": "任务描述", "example": "抖音平台人工智能关键词视频爬取任务"}, "platform": {"type": "string", "description": "爬取平台", "enum": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "weibo", "tieba", "bilibili", "do<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "zhihu", "trendinsight"], "example": "do<PERSON><PERSON>"}, "crawler_type": {"type": "string", "description": "爬虫类型：search-搜索爬虫，detail-详情爬虫，comment-评论爬虫，user-用户爬虫，creator-创作者爬虫，live-直播爬虫，homefeed-首页推荐", "enum": ["search", "detail", "comment", "user", "creator", "live", "homefeed"], "example": "search"}, "status": {"type": "string", "description": "任务状态", "enum": ["PENDING", "RUNNING", "PROCESSING", "PAUSED", "COMPLETED", "SUCCESS", "FAILED", "ERROR", "CANCELLED"], "example": "running"}, "progress": {"type": "integer", "description": "任务进度（百分比）", "minimum": 0, "maximum": 100, "example": 65}, "priority": {"type": "integer", "description": "任务优先级", "minimum": 0, "maximum": 100, "example": 80}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间", "example": "2023-12-31T23:59:59Z"}, "started_at": {"type": "string", "format": "date-time", "description": "开始时间", "nullable": true, "example": "2024-01-01T00:00:00Z"}, "completed_at": {"type": "string", "format": "date-time", "description": "完成时间", "nullable": true, "example": "2024-01-01T01:00:00Z"}, "failed_at": {"type": "string", "format": "date-time", "description": "失败时间", "nullable": true, "example": null}, "total_items": {"type": "integer", "description": "总项目数", "example": 100}, "success_items": {"type": "integer", "description": "成功项目数", "example": 65}, "failed_items": {"type": "integer", "description": "失败项目数", "example": 2}, "error_message": {"type": "string", "description": "错误消息", "nullable": true, "example": ""}, "actual_duration": {"type": "integer", "description": "实际执行时长（秒）", "example": 3600}, "keyword_alias": {"type": "string", "description": "用户自定义关键词别名", "nullable": true, "example": "AI技术"}, "keyword_display_name": {"type": "string", "description": "关键词显示名称（优先使用别名）", "example": "AI技术"}, "keyword_priority": {"type": "integer", "description": "用户关键词优先级", "minimum": 0, "maximum": 100, "example": 85}, "keyword_is_favorite": {"type": "boolean", "description": "是否为收藏关键词", "example": true}, "keyword_usage_count": {"type": "integer", "description": "关键词使用次数", "example": 15}, "keyword_category": {"type": "string", "description": "用户自定义关键词分类", "nullable": true, "example": "技术类"}}}, "UserKeywordListResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "获取关键词监控列表成功"}, "data": {"type": "object", "properties": {"keywords": {"type": "array", "description": "关键词监控列表", "items": {"$ref": "#/components/schemas/UserKeywordInfo"}}, "total": {"type": "integer", "description": "总数量", "example": 25}, "page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页数量", "example": 10}, "total_pages": {"type": "integer", "description": "总页数", "example": 3}, "has_more": {"type": "boolean", "description": "是否有更多数据", "example": true}}}}}, "DeleteUserKeywordRequest": {"type": "object", "required": ["keyword_uuid"], "properties": {"keyword_uuid": {"type": "string", "description": "要删除的关键词关联UUID", "minLength": 32, "maxLength": 32, "pattern": "^[a-f0-9]{32}$", "example": "1234567890abcdef1234567890abcdef"}}}, "DeleteUserKeywordResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "删除关键词成功"}, "data": {"type": "object", "properties": {"keyword_uuid": {"type": "string", "description": "被删除的关键词UUID", "example": "1234567890abcdef1234567890abcdef"}, "keyword_id": {"type": "integer", "description": "关键词ID", "example": 123}, "display_name": {"type": "string", "description": "关键词显示名称", "example": "测试关键词"}, "deleted_at": {"type": "string", "format": "date-time", "description": "删除时间", "example": "2024-01-01T12:00:00Z"}, "message": {"type": "string", "description": "操作结果消息", "example": "关键词删除成功"}}}}}, "GetUserAuthorKeywordListResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "获取author关键词监控列表成功"}, "data": {"type": "object", "properties": {"list": {"type": "array", "description": "作者关键词监控列表", "items": {"$ref": "#/components/schemas/UserAuthorKeywordInfo"}}, "total": {"type": "integer", "description": "总数量", "example": 15}, "page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页数量", "example": 10}, "total_pages": {"type": "integer", "description": "总页数", "example": 2}, "has_more": {"type": "boolean", "description": "是否有更多数据", "example": true}}}}}, "GetUserKeywordListResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "获取关键词监控列表成功"}, "data": {"type": "object", "properties": {"list": {"type": "array", "description": "关键词监控列表", "items": {"$ref": "#/components/schemas/UserKeywordInfo"}}, "total": {"type": "integer", "description": "总数量", "example": 25}, "page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页数量", "example": 10}, "total_pages": {"type": "integer", "description": "总页数", "example": 3}, "has_more": {"type": "boolean", "description": "是否有更多数据", "example": true}}}}}, "UserKeywordInfo": {"type": "object", "description": "用户关键词监控信息", "properties": {"id": {"type": "string", "description": "主键", "example": "1234567890abcdef1234567890abcdef"}, "user_uuid": {"type": "string", "description": "用户UUID", "example": "abcdef1234567890abcdef1234567890"}, "keyword_id": {"type": "integer", "description": "关键词ID (外键关联TrendInsightKeyword)", "example": 1}, "type": {"type": "string", "description": "爬取类型", "enum": ["video", "author"], "default": "video", "example": "video"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间", "example": "2024-01-01T00:00:00Z"}, "updateTime": {"type": "string", "format": "date-time", "description": "更新时间", "example": "2024-01-15T10:30:00Z"}, "is_deleted": {"type": "boolean", "description": "是否删除", "example": false}, "today_count": {"type": "integer", "description": "该关键词当天的数据数量（基于 create_time）", "example": 15}, "total_count": {"type": "integer", "description": "该关键词的总数据数量（不限制时间）", "example": 150}, "trend_keyword": {"type": "object", "description": "关联的关键词详细信息 (当联表查询时返回)", "nullable": true, "properties": {"id": {"type": "integer", "description": "关键词ID", "example": 1}, "keyword": {"type": "string", "description": "关键词文本", "example": "人工智能"}, "keyword_hash": {"type": "string", "description": "关键词哈希值", "example": "5d41402abc4b2a76b9719d911017c592"}, "heat_score": {"type": "number", "format": "float", "description": "热度评分 (float32)", "example": 85.5}, "search_volume": {"type": "integer", "format": "int32", "description": "搜索量 (int32)", "example": 100000}, "trend_direction": {"type": "string", "description": "趋势方向", "enum": ["rising", "falling", "stable", "volatile"], "example": "rising"}, "category": {"type": "string", "description": "关键词分类", "nullable": true, "example": "科技"}, "tags": {"type": "string", "description": "关键词标签（JSON格式）", "nullable": true, "example": "[\"AI\", \"机器学习\", \"深度学习\"]"}, "video_count": {"type": "integer", "format": "int32", "description": "相关视频数 (int32)", "example": 500}, "author_count": {"type": "integer", "format": "int32", "description": "相关作者数 (int32)", "example": 100}, "date": {"type": "string", "format": "date", "description": "统计日期", "example": "2024-01-15"}, "crawl_time": {"type": "string", "description": "爬取时间 (RFC3339格式字符串)", "example": "2024-01-15T10:30:00Z"}, "raw_data": {"type": "string", "description": "原始数据（JSON格式）", "nullable": true, "example": "{\"source\": \"trendinsight\", \"platform\": \"douyin\"}"}, "created_at": {"type": "string", "format": "date-time", "description": "关键词创建时间", "example": "2024-01-01T00:00:00Z"}, "updated_at": {"type": "string", "format": "date-time", "description": "关键词更新时间", "example": "2024-01-15T10:30:00Z"}}}}}, "UserAuthorKeywordInfo": {"type": "object", "description": "用户作者关键词监控信息", "properties": {"id": {"type": "string", "description": "主键", "example": "1234567890abcdef1234567890abcdef"}, "user_uuid": {"type": "string", "description": "用户UUID", "example": "abcdef1234567890abcdef1234567890"}, "keyword_id": {"type": "integer", "description": "关键词ID (外键关联TrendInsightAuthor)", "example": 5}, "type": {"type": "string", "description": "爬取类型", "enum": ["author"], "example": "author"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间", "example": "2024-01-01T00:00:00Z"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间", "example": "2024-01-15T10:30:00Z"}, "today_count": {"type": "integer", "description": "该作者关键词当天的数据数量（基于 create_time）", "example": 8}, "total_count": {"type": "integer", "description": "该作者关键词的总数据数量（不限制时间）", "example": 85}, "trend_author": {"type": "object", "description": "关联的作者详细信息", "nullable": true, "properties": {"id": {"type": "integer", "description": "作者ID", "example": 5}, "user_id": {"type": "string", "description": "用户ID（平台唯一标识）", "example": "douyin_123456"}, "aweme_id": {"type": "string", "description": "抖音ID", "example": "douyin_123456"}, "user_name": {"type": "string", "description": "用户名", "example": "科技博主"}, "user_head_logo": {"type": "string", "description": "用户头像", "example": "https://example.com/avatar.jpg"}, "user_gender": {"type": "string", "description": "用户性别", "example": "男"}, "user_location": {"type": "string", "description": "用户位置", "example": "北京"}, "user_introduction": {"type": "string", "description": "用户简介", "example": "专注AI技术分享"}, "item_count": {"type": "string", "description": "作品数量(原始)", "example": "200"}, "fans_count": {"type": "string", "description": "粉丝数(原始)", "example": "10万"}, "like_count": {"type": "string", "description": "点赞数(原始)", "example": "100万"}, "item_count_int": {"type": "integer", "description": "作品数量(数值)", "example": 200}, "fans_count_int": {"type": "integer", "description": "粉丝数(数值)", "example": 100000}, "like_count_int": {"type": "integer", "description": "点赞数(数值)", "example": 1000000}, "first_tag_name": {"type": "string", "description": "第一标签名称", "example": "科技"}, "second_tag_name": {"type": "string", "description": "第二标签名称", "example": "AI"}, "fans_milestone_create_time": {"type": "string", "description": "粉丝里程碑创建时间", "example": "2024-01-01"}, "user_aweme_url": {"type": "string", "description": "抖音用户链接", "example": "https://www.douyin.com/user/123456"}, "aweme_pic": {"type": "string", "description": "抖音头像图片", "example": "https://example.com/aweme_pic.jpg"}, "platform": {"type": "string", "description": "来源平台", "example": "trendinsight"}, "crawl_time": {"type": "integer", "description": "爬取时间戳", "example": 1640995200}, "source_keyword": {"type": "string", "description": "搜索来源关键字", "example": "科技博主"}, "raw_data": {"type": "string", "description": "原始数据(JSON格式)", "example": "{\"original_data\": \"...\"}"}, "is_deleted": {"type": "boolean", "description": "是否被软删除", "example": false}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间", "example": "2024-01-01T00:00:00.000000Z"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间", "example": "2024-01-15T10:30:00.000000Z"}}}, "display_name": {"type": "string", "description": "显示名称（用户名）", "example": "科技博主"}, "name": {"type": "string", "description": "通用显示名称（兼容性字段）", "example": "科技博主"}}}, "UpsertVideoKeywordRequest": {"type": "object", "required": ["keyword"], "properties": {"keyword": {"type": "string", "minLength": 1, "maxLength": 100, "description": "视频关键词", "example": "人工智能"}}, "description": "视频关键词创建/更新请求"}, "UpsertAuthorKeywordRequest": {"type": "object", "required": ["user_id"], "properties": {"user_id": {"type": "string", "minLength": 1, "maxLength": 128, "description": "用户ID（平台唯一标识）", "example": "douyin_123456"}}, "description": "作者关键词创建/更新请求"}, "UpsertKeywordRequestSimple": {"type": "object", "required": ["keywords"], "properties": {"keywords": {"type": "array", "items": {"type": "string", "minLength": 1, "maxLength": 100}, "minItems": 1, "maxItems": 50, "description": "关键词列表，支持批量操作", "example": ["人工智能", "机器学习", "深度学习"]}}, "description": "简化的关键词创建/更新请求（类型通过路由确定）"}, "UpsertKeywordRequest": {"type": "object", "required": ["keywords", "type"], "properties": {"keywords": {"type": "array", "items": {"type": "string", "minLength": 1, "maxLength": 100}, "minItems": 1, "maxItems": 50, "description": "关键词列表，支持批量操作", "example": ["人工智能", "机器学习", "深度学习"]}, "type": {"type": "string", "description": "爬取类型，指定关键词的用途", "enum": ["video", "author"], "example": "video"}}, "description": "关键词创建/更新请求"}, "UpsertVideoKeywordResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "视频关键词创建/更新成功"}, "data": {"type": "object", "properties": {"keyword": {"type": "string", "description": "创建/更新的关键词", "example": "人工智能"}, "user_keyword": {"type": "object", "description": "用户关键词关联记录", "properties": {"id": {"type": "string", "description": "关联记录UUID"}, "keyword_id": {"type": "integer", "description": "关键词ID"}, "type": {"type": "string", "description": "关键词类型", "example": "video"}}}, "message": {"type": "string", "description": "操作结果消息", "example": "视频关键词创建/更新成功"}}}}, "description": "视频关键词创建/更新响应"}, "UpsertAuthorKeywordResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "作者关键词创建/更新成功"}, "data": {"type": "object", "properties": {"user_id": {"type": "string", "description": "创建/更新的用户ID", "example": "douyin_123456"}, "user_keyword": {"type": "object", "description": "用户关键词关联记录", "properties": {"id": {"type": "string", "description": "关联记录UUID"}, "keyword_id": {"type": "integer", "description": "关键词ID"}, "type": {"type": "string", "description": "关键词类型", "example": "author"}}}, "message": {"type": "string", "description": "操作结果消息", "example": "作者关键词创建/更新成功"}}}}, "description": "作者关键词创建/更新响应"}, "UpsertKeywordResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "关键词创建/更新处理完成"}, "data": {"type": "object", "properties": {"success_count": {"type": "integer", "description": "成功处理的关键词数量", "example": 3}, "failed_count": {"type": "integer", "description": "失败的关键词数量", "example": 1}, "success_keywords": {"type": "array", "items": {"type": "string"}, "description": "成功处理的关键词列表", "example": ["人工智能", "机器学习", "深度学习"]}, "failed_keywords": {"type": "array", "items": {"type": "object", "properties": {"keyword": {"type": "string", "description": "失败的关键词", "example": "无效关键词"}, "error": {"type": "string", "description": "失败原因", "example": "关键词格式不正确"}}}, "description": "失败的关键词及错误信息"}}}}, "description": "关键词创建/更新响应"}, "TrendInsightVideoSearchResponseSchema": {"type": "object", "description": "TrendInsight视频搜索响应模型", "properties": {"videos": {"type": "array", "description": "视频列表", "items": {"$ref": "#/components/schemas/VideoItem"}}, "total": {"type": "integer", "description": "总数量", "example": 100}, "page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页大小", "example": 20}, "has_more": {"type": "boolean", "description": "是否有更多", "example": true}, "keyword": {"type": "string", "description": "搜索关键词", "example": "人工智能"}, "category_id": {"type": "string", "description": "分类ID", "example": "1"}, "sort": {"type": "string", "description": "排序类型", "example": "hot"}, "date_type": {"type": "integer", "description": "日期类型", "example": 1}, "label_type": {"type": "integer", "description": "标签类型", "example": 1}, "duration_type": {"type": "integer", "description": "时长类型", "example": 2}, "total_count": {"type": "integer", "description": "搜索结果总数", "example": 156}, "result_count": {"type": "integer", "description": "返回结果数量", "example": 20}, "search_time": {"type": "number", "description": "搜索耗时(秒)", "example": 0.5}, "avg_play_count": {"type": "integer", "description": "平均播放量", "example": 1000}, "avg_engagement_score": {"type": "number", "description": "平均互动评分", "example": 85.5}, "platform_distribution": {"type": "object", "description": "平台分布", "additionalProperties": {"type": "integer"}}, "duration_distribution": {"type": "object", "description": "时长分布", "additionalProperties": {"type": "integer"}}, "trending_hashtags": {"type": "array", "description": "热门话题", "items": {"type": "string"}}}}, "TrendInsightAuthorSearchResponseSchema": {"type": "object", "description": "TrendInsight作者搜索响应模型", "properties": {"authors": {"type": "array", "description": "作者列表", "items": {"$ref": "#/components/schemas/TrendInsightAuthorSchema"}}, "total": {"type": "integer", "description": "总数量", "example": 50}, "page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页大小", "example": 20}, "has_more": {"type": "boolean", "description": "是否有更多", "example": true}}}, "Likes": {"oneOf": [{"type": "integer", "format": "int64", "description": "点赞数（整数格式）"}, {"type": "string", "description": "点赞数（字符串格式）"}], "description": "点赞数（支持整数或字符串格式）"}, "Index": {"oneOf": [{"type": "integer", "format": "int64", "description": "索引（整数格式）"}, {"type": "string", "description": "索引（字符串格式）"}], "description": "索引（支持整数或字符串格式）"}, "VideoItem": {"type": "object", "description": "视频项数据模型（基于实际VideoItem结构体）", "properties": {"itemId": {"type": "string", "description": "视频ID", "example": "7517079878916885814"}, "title": {"type": "string", "description": "视频标题", "example": "#家有中华田园犬 #毛孩子的日常 旺财大清早就被小黄追着打"}, "url": {"type": "string", "description": "视频链接", "example": "https://www.douyin.com/video/7517079878916885814"}, "thumbnail": {"type": "string", "description": "视频缩略图URL", "example": "https://p6-byteindex-sign.byteimg.com/tos-cn-p-0015/..."}, "likes": {"$ref": "#/components/schemas/Likes"}, "duration": {"type": "integer", "format": "int64", "description": "视频时长(秒)", "example": 36}, "nickname": {"type": "string", "description": "作者昵称", "example": "怀中猫  🍀"}, "avatarUrl": {"type": "string", "description": "作者头像URL", "example": "https://p6-byteindex-sign.byteimg.com/aweme-avatar/..."}, "createTime": {"type": "string", "description": "创建时间", "example": "2025-06-18 08:29:13"}, "index": {"$ref": "#/components/schemas/Index"}, "fans": {"type": "integer", "format": "int64", "description": "粉丝数", "example": 3559}, "isLowFansButHot": {"type": "integer", "format": "int64", "description": "是否低粉但热门", "example": 0}, "isHighLikeRate": {"type": "integer", "format": "int64", "description": "是否高点赞率", "example": 1}, "isHighPlayOverRate": {"type": "integer", "format": "int64", "description": "是否高播放完成率", "example": 0}, "isHighFansRiseRate": {"type": "integer", "format": "int64", "description": "是否高粉丝增长率", "example": 0}}, "required": ["itemId", "title", "url", "thumbnail", "likes", "duration", "nickname", "avatarUrl", "createTime", "index", "fans", "isLowFansButHot", "isHighLikeRate", "isHighPlayOverRate", "isHighFansRiseRate"]}, "TrendInsightVideoResponseSchema": {"type": "object", "description": "TrendInsight视频API响应模型", "properties": {"id": {"type": "integer", "format": "int64", "description": "视频记录ID", "example": 1}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间", "example": "2024-01-15T10:30:00Z"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间", "example": "2024-01-15T10:30:00Z"}, "video_id": {"type": "string", "description": "视频ID", "example": "7517079878916885814"}, "title": {"type": "string", "description": "视频标题", "example": "#家有中华田园犬 #毛孩子的日常 旺财大清早就被小黄追着打"}, "description": {"type": "string", "description": "视频描述"}, "video_url": {"type": "string", "description": "视频链接", "example": "https://www.douyin.com/video/7517079878916885814"}, "cover_url": {"type": "string", "description": "封面链接", "example": "https://p6-byteindex-sign.byteimg.com/tos-cn-p-0015/..."}, "duration": {"type": "integer", "format": "int64", "description": "视频时长(秒)", "example": 36}, "aweme_id": {"type": "string", "description": "抖音ID"}, "user_name": {"type": "string", "description": "用户名", "example": "怀中猫  🍀"}, "author_avatar": {"type": "string", "description": "作者头像", "example": "https://p6-byteindex-sign.byteimg.com/aweme-avatar/..."}, "author_followers": {"type": "integer", "format": "int64", "description": "作者粉丝数", "example": 3559}, "play_count": {"type": "integer", "format": "int64", "description": "播放量"}, "like_count": {"type": "integer", "format": "int64", "description": "点赞数", "example": 963762}, "comment_count": {"type": "integer", "format": "int64", "description": "评论数"}, "share_count": {"type": "integer", "format": "int64", "description": "分享数"}, "collect_count": {"type": "integer", "format": "int64", "description": "收藏数"}, "category": {"type": "string", "description": "分类"}, "category_id": {"type": "string", "description": "分类ID"}, "tags": {"type": "array", "description": "标签列表", "items": {"type": "string"}}, "publish_time": {"type": "string", "description": "发布时间", "example": "2025-06-18 08:29:13"}, "platform": {"type": "string", "description": "平台名称", "example": "do<PERSON><PERSON>"}, "source_keyword": {"type": "string", "description": "源关键词"}}, "required": ["video_id"]}, "TrendInsightAuthorSchema": {"type": "object", "description": "TrendInsight作者响应模型", "properties": {"created_at": {"type": "string", "format": "date-time", "description": "创建时间", "example": "2024-06-19T10:30:00Z"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间", "example": "2024-06-19T10:30:00Z"}, "user_id": {"type": "string", "description": "用户ID", "example": "jdcdjaeighc"}, "user_name": {"type": "string", "description": "用户名", "example": "郑工"}, "user_head_logo": {"type": "string", "description": "用户头像URL", "example": "https://p3.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-i-0813_8fb6569579a042dfbd665507c86ca716.jpeg?from=**********"}, "item_count": {"type": "string", "description": "作品数量（原始字符串格式）", "example": "0"}, "follow_count": {"type": "string", "description": "关注数（原始字符串格式）", "example": "194"}, "like_count": {"type": "string", "description": "点赞数（原始字符串格式）", "example": "1246"}, "item_count_int": {"type": "integer", "description": "作品数量（整数格式，可选）", "example": 0}, "follow_count_int": {"type": "integer", "description": "关注数（整数格式，可选）", "example": 194}, "like_count_int": {"type": "integer", "description": "点赞数（整数格式，可选）", "example": 1246}, "first_tag_name": {"type": "string", "description": "主要分类标签", "example": "未分类（投稿数不足）"}, "second_tag_name": {"type": "string", "description": "次要分类标签", "example": ""}, "aweme_id": {"type": "string", "description": "抖音ID", "example": "1026822424"}, "aweme_url": {"type": "string", "description": "抖音主页URL", "example": "https://www.douyin.com/user/MS4wLjABAAAAml5oJboO7n1OG4BvF3UoivwWR54s2MbE732SZ7hGOmI"}, "platform": {"type": "string", "description": "平台名称（可选）", "example": "do<PERSON><PERSON>"}, "crawl_time": {"type": "integer", "description": "爬取时间戳", "example": 1718794200}, "source_keyword": {"type": "string", "description": "源关键词（可选）", "example": "科技博主"}, "raw_data": {"type": "object", "description": "原始数据（JSON对象）", "additionalProperties": true}}, "required": ["created_at", "updated_at", "user_id", "user_name", "user_head_logo", "item_count", "follow_count", "like_count", "first_tag_name", "second_tag_name", "aweme_id", "aweme_url", "crawl_time", "raw_data"]}, "VideoKeywordStatsResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "获取video关键词统计信息成功"}, "data": {"type": "object", "properties": {"keyword_count": {"type": "integer", "description": "关键词总数", "example": 15}, "active_sources": {"type": "integer", "description": "有数据的数据源数", "example": 12}, "total_videos": {"type": "integer", "description": "所有关键词的视频总数", "example": 15000}, "today_count": {"type": "integer", "description": "今日新增视频数", "example": 150}, "avg_videos_per_keyword": {"type": "number", "description": "平均每个关键词的视频数", "example": 1000.0}, "last_updated": {"type": "string", "description": "统计时间", "example": "2024-01-15 10:30:00"}}}}}, "AuthorKeywordStatsResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "获取author关键词统计信息成功"}, "data": {"type": "object", "properties": {"keyword_count": {"type": "integer", "description": "关键词总数", "example": 8}, "active_sources": {"type": "integer", "description": "有数据的数据源数", "example": 6}, "total_authors": {"type": "integer", "description": "所有关键词的作者总数", "example": 2400}, "today_count": {"type": "integer", "description": "今日新增作者数", "example": 30}, "avg_authors_per_keyword": {"type": "number", "description": "平均每个关键词的作者数", "example": 300.0}, "last_updated": {"type": "string", "description": "统计时间", "example": "2024-01-15 10:30:00"}}}}}, "GetSyncRecordsResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "获取同步记录成功"}, "data": {"type": "object", "properties": {"records": {"type": "array", "description": "同步记录列表", "items": {"$ref": "#/components/schemas/DouyinSyncRecord"}}, "pagination": {"type": "object", "description": "分页信息", "properties": {"page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页记录数", "example": 20}, "total": {"type": "integer", "description": "总记录数", "example": 100}, "total_page": {"type": "integer", "description": "总页数", "example": 5}}}}}}}, "GetSyncRecordRelatedListResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "获取同步记录关联成功"}, "data": {"type": "object", "properties": {"records": {"type": "array", "description": "关联记录列表", "items": {"$ref": "#/components/schemas/UserDouyinCollectSyncRecordRelated"}}, "pagination": {"type": "object", "description": "分页信息", "properties": {"page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页记录数", "example": 20}, "total": {"type": "integer", "description": "总记录数", "example": 100}, "total_page": {"type": "integer", "description": "总页数", "example": 5}}}}}}}, "GetSyncStatsResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "获取同步统计成功"}, "data": {"$ref": "#/components/schemas/DouyinSyncStats"}}}, "GetUserVideoStatsResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "获取视频统计成功"}, "data": {"$ref": "#/components/schemas/UserVideoStats"}}}, "GetSyncRecordRelatedResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "获取同步记录关联成功"}, "data": {"type": "object", "properties": {"records": {"type": "array", "description": "关联记录列表", "items": {"$ref": "#/components/schemas/UserDouyinCollectSyncRecordRelated"}}, "pagination": {"type": "object", "description": "分页信息", "properties": {"page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页记录数", "example": 20}, "total": {"type": "integer", "description": "总记录数", "example": 100}, "total_page": {"type": "integer", "description": "总页数", "example": 5}}}, "sync_record_uuid": {"type": "string", "description": "同步记录UUID", "example": "1234567890abcdef1234567890abcdef"}}}}}, "UserDouyinCollectSyncRecordRelated": {"type": "object", "properties": {"id": {"type": "integer", "description": "关联记录ID", "example": 1}, "uuid": {"type": "string", "description": "关联记录UUID", "example": "related_1234567890abcdef1234567890abcdef"}, "user_uuid": {"type": "string", "description": "用户UUID", "example": "user_1234567890abcdef1234567890abcdef"}, "sync_record_uuid": {"type": "string", "description": "同步记录UUID", "example": "1234567890abcdef1234567890abcdef"}, "aweme_id": {"type": "string", "description": "抖音视频ID", "example": "7123456789012345678"}, "create_time": {"type": "string", "format": "date-time", "description": "创建时间", "example": "2024-01-15T10:30:00Z"}, "update_time": {"type": "string", "format": "date-time", "description": "更新时间", "example": "2024-01-15T10:30:00Z"}, "is_deleted": {"type": "boolean", "description": "是否已删除", "example": false}, "douyin_aweme": {"allOf": [{"$ref": "#/components/schemas/Douyin<PERSON>weme"}], "description": "关联的抖音视频详细信息，如果为null表示视频信息未找到", "nullable": true}}}, "SyncCollectsResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "同步成功，新增 5 个视频"}}}, "DouyinSyncRecord": {"type": "object", "properties": {"id": {"type": "integer", "description": "记录ID", "example": 1}, "uuid": {"type": "string", "description": "记录UUID", "example": "1234567890abcdef1234567890abcdef"}, "user_uuid": {"type": "string", "description": "用户UUID", "example": "user_1234567890abcdef1234567890abcdef"}, "sync_count": {"type": "integer", "description": "同步视频数量", "example": 5}, "sync_status": {"type": "string", "description": "同步状态", "enum": ["SUCCESS", "FAILED"], "example": "SUCCESS"}, "sync_time": {"type": "string", "format": "date-time", "description": "同步时间", "example": "2024-01-15T10:30:00Z"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间", "example": "2024-01-15T10:30:00Z"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间", "example": "2024-01-15T10:30:00Z"}}}, "DouyinSyncStats": {"type": "object", "properties": {"total_records": {"type": "integer", "description": "总同步记录数", "example": 100}, "success_records": {"type": "integer", "description": "成功同步记录数", "example": 95}, "failed_records": {"type": "integer", "description": "失败同步记录数", "example": 5}, "total_synced_videos": {"type": "integer", "description": "总同步视频数", "example": 500}, "success_rate": {"type": "number", "format": "float", "description": "同步成功率（百分比）", "example": 95.0}, "avg_videos_per_sync": {"type": "number", "format": "float", "description": "平均每次同步视频数", "example": 5.26}, "last_sync_time": {"type": "string", "format": "date-time", "description": "最后同步时间", "example": "2024-01-15T10:30:00Z"}, "statistics_period": {"type": "integer", "description": "统计周期（天数）", "example": 30}}}, "UserVideoStats": {"type": "object", "description": "用户视频统计信息，基于 user_inbox_video_related 表统计", "properties": {"days": {"type": "integer", "description": "统计天数（保留兼容性参数）", "example": 30}, "total_records": {"type": "integer", "description": "用户视频总记录数（排除软删除）", "example": 150}, "source_stats": {"type": "object", "description": "按来源类型分组的统计信息", "properties": {"collect": {"type": "integer", "description": "收藏夹类型视频数量", "example": 80}, "author": {"type": "integer", "description": "作者类型视频数量", "example": 45}, "video": {"type": "integer", "description": "关键词搜索类型视频数量", "example": 25}}, "required": ["collect", "author", "video"]}, "latest_times": {"type": "object", "description": "每种来源类型的最新记录时间", "properties": {"collect": {"type": "string", "format": "date-time", "description": "收藏夹类型最新记录时间", "example": "2024-01-15T10:30:00Z", "nullable": true}, "author": {"type": "string", "format": "date-time", "description": "作者类型最新记录时间", "example": "2024-01-14T15:20:00Z", "nullable": true}, "video": {"type": "string", "format": "date-time", "description": "关键词搜索类型最新记录时间", "example": "2024-01-13T09:15:00Z", "nullable": true}}, "required": ["collect", "author", "video"]}}, "required": ["days", "total_records", "source_stats", "latest_times"]}, "BindDouyinCookieRequest": {"type": "object", "required": ["cookie"], "properties": {"cookie": {"type": "string", "description": "抖音Cookie字符串", "example": "sessionid=abc123; tt_webid=xyz789; odin_tt=def456"}}}, "DouyinAweme": {"type": "object", "description": "抖音视频详细信息", "properties": {"ID": {"type": "integer", "description": "视频记录ID", "example": 1}, "UserID": {"type": "string", "description": "用户ID", "example": "1691476220714126"}, "SecUID": {"type": "string", "description": "用户sec_uid", "example": "MS4wLjABAAAATvZSy52IJhciYEEddmA36TZ0JWzDCQv98DZick-GbYlPe1ML-PirtAwzj9IKWddd"}, "ShortUserID": {"type": "string", "description": "用户短ID", "example": ""}, "UserUniqueID": {"type": "string", "description": "用户唯一ID", "example": ""}, "Nickname": {"type": "string", "description": "用户昵称", "example": "胡慢慢"}, "Avatar": {"type": "string", "description": "用户头像地址", "example": "https://p3-pc.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-i-0813c001_oofmmL9EAfDjuc8EZEFkjIAkAkAGQgCpgbANgA.jpeg?from=327834062"}, "UserSignature": {"type": "string", "description": "用户签名", "example": ""}, "IPLocation": {"type": "string", "description": "评论时的IP地址", "example": ""}, "AddTs": {"type": "integer", "format": "int64", "description": "记录添加时间戳", "example": 0}, "LastModifyTs": {"type": "integer", "format": "int64", "description": "记录最后修改时间戳", "example": 0}, "AwemeID": {"type": "string", "description": "视频ID", "example": "7509429111140420874"}, "AwemeType": {"type": "string", "description": "视频类型", "example": "0"}, "Title": {"type": "string", "description": "视频标题", "example": "开局一个碗，从董天宝的视角看太极张三丰"}, "Desc": {"type": "string", "description": "视频描述", "example": "开局一个碗，从董天宝的视角看太极张三丰 #太极张三丰 #独白 #香港电影"}, "CreateTime": {"type": "string", "format": "date-time", "description": "视频发布时间", "example": "2022-01-01T00:00:00Z"}, "LikedCount": {"type": "string", "description": "视频点赞数", "example": "65954"}, "CommentCount": {"type": "string", "description": "视频评论数", "example": "1832"}, "ShareCount": {"type": "string", "description": "视频分享数", "example": "3652"}, "CollectedCount": {"type": "string", "description": "视频收藏数", "example": "6095"}, "AwemeURL": {"type": "string", "description": "视频详情页URL", "example": "https://www.iesdouyin.com/share/video/7509429111140420874/?region=CN&mid=7509429671579241267&u_code=1j5e1iiim7il&did=MS4wLjABAAAABZRr28wVdkSMyhDKjTsWMcrU1HeWHUaU11_4gbfzUOcobe1HiRQ3vZNyzZ9UHWfi&iid=MS4wLjABAAAANwkJuWIRFOzg5uCpDRpMj4OX-QryoDgn-yYlXQnRwQQ&with_sec_did=1&video_share_track_ver=&titleType=title&share_sign=bCtk9fb69OCmzbh9PVoR7GYUhakGM90kP657bi4cgoY-&share_version=170400&ts=**********&from_aid=6383&from_ssr=1"}, "CoverURL": {"type": "string", "description": "视频封面图URL", "example": "https://p3-pc-sign.douyinpic.com/tos-cn-i-dy/b48afb45639b441395648b5e73377dee~tplv-dy-cropcenter:323:430.jpeg?lk3s=138a59ce&x-expires=**********&x-signature=Zkek3oPyA35me2WA6uHqKu16ojo%3D&from=327834062&s=PackSourceEnum_COLLECTION&se=true&sh=323_430&sc=cover&biz_tag=pcweb_cover&l=2025070223504777EDDC580CE694433A0D"}, "VideoDownloadURL": {"type": "string", "description": "视频下载地址", "example": "https://v3-web.douyinvod.com/e9167f11bad193d88dbdf468b643c947/686581d8/video/tos/cn/tos-cn-ve-15/oMH2IALiYuPY2EBbAg4eAfUuWv1e1gCDeKZ28F/?a=6383&ch=42&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C3&cv=1&br=2304&bt=2304&cs=0&ds=4&ft=AJkeU_TLRR0s~hC32D12Nc.xBiGNbLeSYfdU_4cE9W_2Nv7TGW&mime_type=video_mp4&qs=0&rc=OWY3ODllZzM2aTg1ZjY8ZEBpam1xb3Y5cjNnMzMzNGkzM0AtYjQyNGBeXjMxMTBfLWBfYSNhMnJpMmRrZ29hLS1kLS9zcw%3D%3D&btag=c0000e00030000&cquery=101r_100B_100H_100K_100o&dy_q=**********&feature_id=46a7bb47b4fd1280f3d3825bf2b29388&l=2025070223504777EDDC580CE694433A0D"}, "SourceKeyword": {"type": "string", "description": "搜索来源关键字", "example": ""}}}, "BindDouyinCookieResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "cookie 绑定成功"}, "data": {"type": "boolean", "description": "绑定结果", "example": true}}}, "GetVideoDetailResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "获取视频详情成功"}, "data": {"type": "object", "description": "视频详情数据", "properties": {"aweme_id": {"type": "string", "description": "抖音视频ID", "example": "7123456789012345678"}, "desc": {"type": "string", "description": "视频描述", "example": "分享一个有趣的科技小知识"}, "title": {"type": "string", "description": "视频标题", "example": "你知道吗？人工智能的发展历史"}, "nickname": {"type": "string", "description": "作者昵称", "example": "科技小达人"}, "user_id": {"type": "string", "description": "作者用户ID", "example": "123456789"}, "aweme_url": {"type": "string", "description": "视频详情页URL", "example": "https://www.douyin.com/video/7123456789012345678"}, "cover_url": {"type": "string", "description": "视频封面图URL", "example": "https://p3.douyinpic.com/img/aweme-cover/..."}, "video_download_url": {"type": "string", "description": "视频下载地址", "example": "https://aweme.snssdk.com/aweme/v1/play/..."}, "create_time": {"type": "integer", "format": "int64", "description": "视频发布时间戳", "example": **********}, "liked_count": {"type": "string", "description": "视频点赞数", "example": "1.2w"}, "comment_count": {"type": "string", "description": "视频评论数", "example": "856"}, "share_count": {"type": "string", "description": "视频分享数", "example": "234"}, "collected_count": {"type": "string", "description": "视频收藏数", "example": "567"}, "duration": {"type": "integer", "description": "视频时长（秒）", "example": 45}, "width": {"type": "integer", "description": "视频宽度", "example": 720}, "height": {"type": "integer", "description": "视频高度", "example": 1280}}}}}, "VideoRelatedListResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "message": {"type": "string", "description": "响应消息", "example": "查询成功"}, "data": {"type": "object", "properties": {"list": {"type": "array", "description": "视频关联记录列表", "items": {"$ref": "#/components/schemas/VideoRelatedRecord"}}, "total": {"type": "integer", "description": "总记录数", "example": 150}, "page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页数量", "example": 20}}}, "token": {"type": "string", "description": "刷新后的token（如果需要）", "example": "refreshed_token_if_needed"}, "time": {"type": "integer", "description": "响应时间戳", "example": 1672531200000}}}, "VideoRelatedRecord": {"type": "object", "properties": {"uuid": {"type": "string", "description": "记录的唯一标识符", "example": "184e796adbd519e0-184e796adbd519e1"}, "source_id": {"type": "string", "description": "来源ID（如收藏夹ID、关键词ID等）", "example": "7495633625980131112"}, "aweme_id": {"type": "string", "description": "抖音视频ID", "example": "7509429111140420874"}, "source_type": {"type": "string", "description": "来源类型（返回大写枚举值）", "enum": ["KEYWORD", "AUTHOR", "COLLECT"], "example": "COLLECT"}, "handle_status": {"type": "string", "description": "处理状态", "enum": ["PENDING", "SUCCESS", "FAILED"], "example": "PENDING"}, "publish_time": {"type": "string", "format": "date-time", "description": "视频发布时间（ISO 8601格式）", "example": "2025-07-02T23:40:04+08:00"}, "createTime": {"type": "string", "format": "date-time", "description": "记录创建时间（ISO 8601格式）", "example": "2025-07-02T23:40:04+08:00"}, "updateTime": {"type": "string", "format": "date-time", "description": "记录更新时间（ISO 8601格式）", "example": "2025-07-02T23:40:04+08:00"}, "douyin_aweme": {"allOf": [{"$ref": "#/components/schemas/Douyin<PERSON>weme"}], "description": "关联的抖音视频详细信息，包含视频标题、描述、作者、统计数据等。如果为null表示视频信息未找到或跨库查询失败", "nullable": true}, "trend_score": {"type": "number", "format": "double", "description": "趋势评分，计算方式为 int(trendinsight_video.trend_radio * trendinsight_video.trend_score)，结果保留整数，如果不存在趋势数据则为null", "example": 85, "nullable": true}}}, "VideoRelatedStatsResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "message": {"type": "string", "description": "响应消息", "example": "查询成功"}, "data": {"type": "object", "properties": {"total": {"type": "integer", "description": "总记录数", "example": 150}, "source_type_stats": {"type": "object", "description": "按来源类型分组统计", "properties": {"COLLECT": {"type": "integer", "description": "收藏夹类型数量", "example": 80}, "KEYWORD": {"type": "integer", "description": "关键词类型数量", "example": 45}, "AUTHOR": {"type": "integer", "description": "作者类型数量", "example": 25}}, "required": ["COLLECT", "KEYWORD", "AUTHOR"]}, "latest_times": {"type": "object", "description": "各个来源类型最新一条数据的创建时间", "properties": {"COLLECT": {"type": "string", "format": "date-time", "description": "收藏夹类型最新记录时间", "example": "2024-01-15T10:30:00Z", "nullable": true}, "KEYWORD": {"type": "string", "format": "date-time", "description": "关键词类型最新记录时间", "example": "2024-01-14T15:20:00Z", "nullable": true}, "AUTHOR": {"type": "string", "format": "date-time", "description": "作者类型最新记录时间", "example": "2024-01-13T09:15:00Z", "nullable": true}}, "required": ["COLLECT", "KEYWORD", "AUTHOR"]}}}, "token": {"type": "string", "description": "刷新后的token（如果需要）", "example": "refreshed_token_if_needed"}, "time": {"type": "integer", "description": "响应时间戳", "example": 1672531200000}}}, "AddVideoToAssetsRequest": {"type": "object", "required": ["record_uuid"], "properties": {"record_uuid": {"type": "string", "description": "视频关联记录UUID", "minLength": 32, "maxLength": 32, "pattern": "^[a-f0-9]{32}$", "example": "1234567890abcdef1234567890abcdef"}}}, "AddVideoToAssetsResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "msg": {"type": "string", "description": "响应消息", "example": "视频已成功添加到素材库"}, "data": {"type": "object", "properties": {"asset_uuid": {"type": "string", "description": "创建的素材记录UUID", "example": "asset-uuid-456"}, "video_id": {"type": "string", "description": "抖音视频ID", "example": "7123456789012345678"}, "video_title": {"type": "string", "description": "视频标题", "example": "人工智能技术发展趋势分析"}, "author": {"type": "string", "description": "视频作者", "example": "科技博主小王"}, "source": {"type": "string", "description": "素材来源类型", "enum": ["COLLECTION", "MANUAL"], "example": "COLLECTION"}, "record_status": {"type": "string", "description": "记录处理状态", "enum": ["SUCCESS"], "example": "SUCCESS"}}, "required": ["asset_uuid", "video_id", "video_title", "author", "source", "record_status"]}, "token": {"type": "string", "description": "刷新后的token（如果需要）", "example": "refreshed_token_if_needed"}, "time": {"type": "integer", "description": "响应时间戳", "example": 1672531200000}}, "required": ["code", "msg", "data"]}, "UserDouyinCollectSyncRecordRelatedWithVideo": {"type": "object", "description": "用户抖音收藏同步记录关联，包含视频详细信息", "allOf": [{"$ref": "#/components/schemas/UserDouyinCollectSyncRecordRelated"}, {"type": "object", "properties": {"douyin_aweme": {"$ref": "#/components/schemas/Douyin<PERSON>weme", "description": "关联的抖音视频信息"}, "trend_score": {"type": "number", "format": "double", "description": "趋势评分，计算方式为 int(trendinsight_video.trend_radio * trendinsight_video.trend_score)，结果保留整数，如果不存在趋势数据则为null", "example": 85, "nullable": true}}}]}, "UserInboxVideoRelated": {"type": "object", "description": "用户收件箱视频关联记录", "properties": {"uuid": {"type": "string", "description": "主键UUID", "example": "1234567890abcdef1234567890abcdef"}, "create_time": {"type": "string", "format": "date-time", "description": "创建时间", "example": "2025-01-03T10:30:00+08:00"}, "update_time": {"type": "string", "format": "date-time", "description": "更新时间", "example": "2025-01-03T10:30:00+08:00"}, "is_deleted": {"type": "boolean", "description": "是否删除", "example": false}, "user_uuid": {"type": "string", "description": "用户UUID", "example": "user1234567890abcdef1234567890ab"}, "source_id": {"type": "string", "description": "源ID（关键词ID、作者ID或收藏夹ID）", "example": "MS4wLjABAAAA1234567890abcdef"}, "source_type": {"type": "string", "description": "来源类型（返回大写枚举值）", "enum": ["KEYWORD", "AUTHOR", "COLLECT"], "example": "AUTHOR"}, "aweme_id": {"type": "string", "description": "抖音视频ID", "example": "7123456789012345678"}, "publish_time": {"type": "string", "format": "date-time", "description": "视频发布时间", "example": "2025-01-02T15:30:00+08:00"}, "handle_status": {"type": "string", "description": "处理状态", "enum": ["pending", "success", "failed"], "example": "success"}}}, "UserInboxVideoRelatedWithAweme": {"type": "object", "description": "用户收件箱视频关联记录，包含抖音视频详细信息", "allOf": [{"$ref": "#/components/schemas/UserInboxVideoRelated"}, {"type": "object", "properties": {"douyin_aweme": {"allOf": [{"$ref": "#/components/schemas/Douyin<PERSON>weme"}], "description": "关联的抖音视频详细信息，如果为null表示视频信息未找到或跨库查询失败", "nullable": true}, "trend_score": {"type": "number", "format": "double", "description": "趋势评分，计算方式为 int(trend_radio * trend_score)，结果保留整数，如果不存在趋势数据则为null", "example": 85, "nullable": true}}}]}, "TrendInsightVideo": {"type": "object", "description": "趋势洞察视频信息", "properties": {"id": {"type": "integer", "description": "主键ID", "example": 12345}, "video_id": {"type": "string", "description": "视频ID", "example": "7123456789012345678"}, "title": {"type": "string", "description": "视频标题", "example": "人工智能技术发展趋势分析"}, "description": {"type": "string", "description": "视频描述", "example": "详细分析人工智能技术的最新发展趋势"}, "video_url": {"type": "string", "description": "视频链接", "example": "https://example.com/video.mp4"}, "cover_url": {"type": "string", "description": "封面链接", "example": "https://example.com/cover.jpg"}, "duration": {"type": "integer", "description": "视频时长(秒)", "example": 120}, "author_id": {"type": "string", "description": "作者ID", "example": "123456789"}, "author_name": {"type": "string", "description": "作者名称", "example": "科技博主小王"}, "author_avatar": {"type": "string", "description": "作者头像", "example": "https://example.com/avatar.jpg"}, "author_followers": {"type": "integer", "description": "作者粉丝数", "example": 100000}, "play_count": {"type": "integer", "description": "播放量", "example": 50000}, "like_count": {"type": "integer", "description": "点赞数", "example": 1200}, "comment_count": {"type": "integer", "description": "评论数", "example": 300}, "share_count": {"type": "integer", "description": "分享数", "example": 150}, "collect_count": {"type": "integer", "description": "收藏数", "example": 80}, "category": {"type": "string", "description": "分类", "example": "科技"}, "category_id": {"type": "string", "description": "分类ID", "example": "1"}, "platform": {"type": "string", "description": "来源平台", "example": "do<PERSON><PERSON>"}, "platform_video_id": {"type": "string", "description": "平台原始视频ID", "example": "7123456789012345678"}, "publish_time": {"type": "integer", "description": "发布时间戳", "example": 1672531200}, "crawl_time": {"type": "integer", "description": "爬取时间戳", "example": 1672531200}, "trend_score": {"type": "number", "format": "double", "description": "趋势评分", "example": 85.5}, "trend_radio": {"type": "number", "format": "double", "description": "趋势权重系数", "example": 1.2}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间", "example": "2023-01-01T12:00:00Z"}, "updated_at": {"type": "string", "format": "date-time", "description": "更新时间", "example": "2023-01-01T12:00:00Z"}}, "required": ["id", "video_id", "author_id", "platform", "publish_time", "crawl_time", "trend_score", "trend_radio"]}}}}