package main

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
)

// OpenAPISpec OpenAPI 规范结构
type OpenAPISpec struct {
	Components struct {
		Schemas map[string]interface{} `json:"schemas"`
	} `json:"components"`
}

// TestOpenAPITotalCount 测试 OpenAPI 文档中的 total_count 字段
func TestOpenAPITotalCount() {
	fmt.Println("=== 测试 OpenAPI 文档中的 total_count 字段 ===")

	// 获取当前工作目录
	pwd, err := os.Getwd()
	if err != nil {
		fmt.Printf("❌ 获取工作目录失败: %s\n", err.Error())
		return
	}

	// 构建 OpenAPI 文档路径
	openAPIPath := filepath.Join(pwd, "resource", "static", "openapi.json")
	fmt.Printf("OpenAPI 文档路径: %s\n", openAPIPath)

	// 检查文件是否存在
	if _, err := os.Stat(openAPIPath); os.IsNotExist(err) {
		fmt.Printf("❌ OpenAPI 文档文件不存在: %s\n", openAPIPath)
		return
	}

	// 读取文件内容
	content, err := os.ReadFile(openAPIPath)
	if err != nil {
		fmt.Printf("❌ 读取 OpenAPI 文档失败: %s\n", err.Error())
		return
	}

	// 解析 JSON
	var spec OpenAPISpec
	if err := json.Unmarshal(content, &spec); err != nil {
		fmt.Printf("❌ OpenAPI 文档格式错误: %s\n", err.Error())
		return
	}

	fmt.Printf("✅ OpenAPI 文档解析成功\n")

	// 测试 UserKeywordInfo 模式
	fmt.Println("\n--- 测试 UserKeywordInfo 模式 ---")
	if userKeywordInfo, exists := spec.Components.Schemas["UserKeywordInfo"]; exists {
		fmt.Printf("✅ UserKeywordInfo 模式存在\n")
		
		// 转换为 map 以便检查属性
		if schemaMap, ok := userKeywordInfo.(map[string]interface{}); ok {
			if properties, ok := schemaMap["properties"].(map[string]interface{}); ok {
				// 检查 today_count 字段
				if todayCount, exists := properties["today_count"]; exists {
					fmt.Printf("✅ today_count 字段存在\n")
					if fieldMap, ok := todayCount.(map[string]interface{}); ok {
						if desc, ok := fieldMap["description"].(string); ok {
							fmt.Printf("  描述: %s\n", desc)
						}
						if example, ok := fieldMap["example"]; ok {
							fmt.Printf("  示例: %v\n", example)
						}
					}
				} else {
					fmt.Printf("❌ today_count 字段不存在\n")
				}

				// 检查 total_count 字段（新增）
				if totalCount, exists := properties["total_count"]; exists {
					fmt.Printf("✅ total_count 字段存在（新增）\n")
					if fieldMap, ok := totalCount.(map[string]interface{}); ok {
						if desc, ok := fieldMap["description"].(string); ok {
							fmt.Printf("  描述: %s\n", desc)
						}
						if example, ok := fieldMap["example"]; ok {
							fmt.Printf("  示例: %v\n", example)
						}
					}
				} else {
					fmt.Printf("❌ total_count 字段不存在\n")
				}
			}
		}
	} else {
		fmt.Printf("❌ UserKeywordInfo 模式不存在\n")
	}

	// 测试 UserAuthorKeywordInfo 模式
	fmt.Println("\n--- 测试 UserAuthorKeywordInfo 模式 ---")
	if userAuthorKeywordInfo, exists := spec.Components.Schemas["UserAuthorKeywordInfo"]; exists {
		fmt.Printf("✅ UserAuthorKeywordInfo 模式存在\n")
		
		// 转换为 map 以便检查属性
		if schemaMap, ok := userAuthorKeywordInfo.(map[string]interface{}); ok {
			if properties, ok := schemaMap["properties"].(map[string]interface{}); ok {
				// 检查 today_count 字段
				if todayCount, exists := properties["today_count"]; exists {
					fmt.Printf("✅ today_count 字段存在\n")
					if fieldMap, ok := todayCount.(map[string]interface{}); ok {
						if desc, ok := fieldMap["description"].(string); ok {
							fmt.Printf("  描述: %s\n", desc)
						}
						if example, ok := fieldMap["example"]; ok {
							fmt.Printf("  示例: %v\n", example)
						}
					}
				} else {
					fmt.Printf("❌ today_count 字段不存在\n")
				}

				// 检查 total_count 字段（新增）
				if totalCount, exists := properties["total_count"]; exists {
					fmt.Printf("✅ total_count 字段存在（新增）\n")
					if fieldMap, ok := totalCount.(map[string]interface{}); ok {
						if desc, ok := fieldMap["description"].(string); ok {
							fmt.Printf("  描述: %s\n", desc)
						}
						if example, ok := fieldMap["example"]; ok {
							fmt.Printf("  示例: %v\n", example)
						}
					}
				} else {
					fmt.Printf("❌ total_count 字段不存在\n")
				}
			}
		}
	} else {
		fmt.Printf("❌ UserAuthorKeywordInfo 模式不存在\n")
	}

	// 验证字段类型
	fmt.Println("\n--- 验证字段类型 ---")
	requiredFields := []string{"today_count", "total_count"}
	schemas := []string{"UserKeywordInfo", "UserAuthorKeywordInfo"}
	
	allFieldsValid := true
	for _, schemaName := range schemas {
		fmt.Printf("\n检查模式: %s\n", schemaName)
		if schema, exists := spec.Components.Schemas[schemaName]; exists {
			if schemaMap, ok := schema.(map[string]interface{}); ok {
				if properties, ok := schemaMap["properties"].(map[string]interface{}); ok {
					for _, fieldName := range requiredFields {
						if field, exists := properties[fieldName]; exists {
							if fieldMap, ok := field.(map[string]interface{}); ok {
								if fieldType, ok := fieldMap["type"].(string); ok {
									if fieldType == "integer" {
										fmt.Printf("  ✅ %s: 类型正确 (integer)\n", fieldName)
									} else {
										fmt.Printf("  ❌ %s: 类型错误 (%s), 应为 integer\n", fieldName, fieldType)
										allFieldsValid = false
									}
								} else {
									fmt.Printf("  ❌ %s: 缺少类型定义\n", fieldName)
									allFieldsValid = false
								}
							}
						} else {
							fmt.Printf("  ❌ %s: 字段不存在\n", fieldName)
							allFieldsValid = false
						}
					}
				}
			}
		}
	}

	// 最终结果
	fmt.Println("\n--- 测试结果 ---")
	if allFieldsValid {
		fmt.Println("✅ 所有测试通过！OpenAPI 文档中的 total_count 字段已成功添加")
		fmt.Println("✅ UserKeywordInfo 和 UserAuthorKeywordInfo 模式都包含了 total_count 字段")
		fmt.Println("✅ 字段类型定义正确 (integer)")
		fmt.Println("✅ 字段描述和示例值已添加")
	} else {
		fmt.Println("❌ 部分测试失败，请检查 OpenAPI 文档")
	}

	fmt.Println("\n=== 测试完成 ===")
}

func main() {
	// 运行测试
	TestOpenAPITotalCount()
}
