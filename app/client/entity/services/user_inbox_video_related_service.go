package services

import (
	"fmt"
	"gofly/app/client/cons"
	"gofly/app/client/entity"
	"gofly/setting"
	"gofly/utils/gf"
	"gofly/utils/tools/gctx"
	"time"
)

// VideoItemWithTime 包含视频ID和创建时间的结构体
type VideoItemWithTime struct {
	AwemeID    string
	CreateTime *int64 // Unix时间戳，可为空
}

// UserInboxVideoRelatedService 用户收件箱视频关联服务
type UserInboxVideoRelatedService struct{}

// NewUserInboxVideoRelatedService 创建用户收件箱视频关联服务实例
func NewUserInboxVideoRelatedService() *UserInboxVideoRelatedService {
	return &UserInboxVideoRelatedService{}
}

// BatchCreateFromCollectSync 从收藏夹同步批量创建视频记录
// awemeIds: 抖音视频ID列表
// userUUID: 用户UUID
// sourceId: 来源ID（通常是收藏夹ID或关键词ID）
func (s *UserInboxVideoRelatedService) BatchCreateFromCollectSync(userUUID, sourceId string, awemeIds []string) error {
	var gfctx = gctx.New()

	gf.Log().Info(gfctx, "开始批量创建视频关联记录", gf.Map{
		"user_uuid":   userUUID,
		"source_id":   sourceId,
		"aweme_ids":   awemeIds,
		"aweme_count": len(awemeIds),
	})

	if len(awemeIds) == 0 {
		gf.Log().Info(gfctx, "aweme_ids为空，跳过创建", gf.Map{
			"user_uuid": userUUID,
			"source_id": sourceId,
		})
		return nil
	}

	// 检查已存在的记录，避免重复创建
	existingAwemeIds, err := s.getExistingAwemeIds(userUUID, awemeIds)
	if err != nil {
		gf.Log().Error(gfctx, "检查已存在记录失败", gf.Map{
			"user_uuid": userUUID,
			"source_id": sourceId,
			"aweme_ids": awemeIds,
			"error":     err.Error(),
		})
		return fmt.Errorf("检查已存在记录失败: %w", err)
	}

	gf.Log().Info(gfctx, "检查已存在记录完成", gf.Map{
		"user_uuid":          userUUID,
		"source_id":          sourceId,
		"total_aweme_count":  len(awemeIds),
		"existing_aweme_ids": existingAwemeIds,
		"existing_count":     len(existingAwemeIds),
	})

	// 过滤出需要创建的新记录
	var newAwemeIds []string
	existingMap := make(map[string]bool)
	for _, id := range existingAwemeIds {
		existingMap[id] = true
	}

	for _, awemeId := range awemeIds {
		if !existingMap[awemeId] {
			newAwemeIds = append(newAwemeIds, awemeId)
		}
	}

	gf.Log().Info(gfctx, "过滤新记录完成", gf.Map{
		"user_uuid":      userUUID,
		"source_id":      sourceId,
		"new_aweme_ids":  newAwemeIds,
		"new_count":      len(newAwemeIds),
		"existing_count": len(existingAwemeIds),
	})

	if len(newAwemeIds) == 0 {
		gf.Log().Info(gfctx, "所有记录都已存在，跳过创建", gf.Map{
			"user_uuid":      userUUID,
			"source_id":      sourceId,
			"existing_count": len(existingAwemeIds),
		})
		return nil // 所有记录都已存在
	}

	// 批量创建新记录
	var records []*entity.UserInboxVideoRelated
	now := time.Now()

	gf.Log().Info(gfctx, "开始构建新记录", gf.Map{
		"user_uuid":     userUUID,
		"source_id":     sourceId,
		"new_aweme_ids": newAwemeIds,
		"new_count":     len(newAwemeIds),
	})

	for _, awemeId := range newAwemeIds {
		record := &entity.UserInboxVideoRelated{
			UserUUID:     userUUID,
			SourceId:     sourceId,
			AwemeId:      awemeId,
			SourceType:   entity.SourceTypeCollect, // 收藏夹同步标记为收藏夹类型
			PublishTime:  now,                      // 默认发布时间，后续可更新
			HandleStatus: cons.Pending.Value,       // 默认为待处理状态
		}

		// 执行创建前钩子
		if err := record.BeforeCreate(); err != nil {
			gf.Log().Error(gfctx, "记录创建前钩子失败", gf.Map{
				"user_uuid": userUUID,
				"source_id": sourceId,
				"aweme_id":  awemeId,
				"error":     err.Error(),
			})
			return fmt.Errorf("记录 %s 创建前钩子失败: %w", awemeId, err)
		}

		records = append(records, record)
	}

	gf.Log().Info(gfctx, "记录构建完成，开始批量插入", gf.Map{
		"user_uuid":     userUUID,
		"source_id":     sourceId,
		"records_count": len(records),
	})

	// 批量插入数据库
	if err := s.batchInsert(records); err != nil {
		gf.Log().Error(gfctx, "批量插入失败", gf.Map{
			"user_uuid":     userUUID,
			"source_id":     sourceId,
			"records_count": len(records),
			"error":         err.Error(),
		})
		return fmt.Errorf("批量插入失败: %w", err)
	}

	gf.Log().Info(gfctx, "批量创建视频关联记录成功", gf.Map{
		"user_uuid":      userUUID,
		"source_id":      sourceId,
		"created_count":  len(records),
		"existing_count": len(existingAwemeIds),
		"total_count":    len(awemeIds),
	})

	return nil
}

// BatchCreateFromCollectSyncWithTime 从收藏夹同步批量创建视频记录（支持创建时间）
// videoItems: 包含视频ID和创建时间的列表
// userUUID: 用户UUID
// sourceId: 来源ID（通常是收藏夹ID）
func (s *UserInboxVideoRelatedService) BatchCreateFromCollectSyncWithTime(userUUID, sourceId string, videoItems []VideoItemWithTime) error {
	var gfctx = gctx.New()

	gf.Log().Info(gfctx, "开始批量创建视频关联记录（带创建时间）", gf.Map{
		"user_uuid":         userUUID,
		"source_id":         sourceId,
		"video_items":       videoItems,
		"video_items_count": len(videoItems),
	})

	if len(videoItems) == 0 {
		gf.Log().Info(gfctx, "videoItems为空，跳过创建", gf.Map{
			"user_uuid": userUUID,
			"source_id": sourceId,
		})
		return nil
	}

	// 提取awemeIds用于检查已存在的记录
	var awemeIds []string
	for _, item := range videoItems {
		if item.AwemeID != "" {
			awemeIds = append(awemeIds, item.AwemeID)
		}
	}

	// 检查已存在的记录，避免重复创建
	existingAwemeIds, err := s.getExistingAwemeIds(userUUID, awemeIds)
	if err != nil {
		gf.Log().Error(gfctx, "检查已存在记录失败", gf.Map{
			"user_uuid": userUUID,
			"source_id": sourceId,
			"aweme_ids": awemeIds,
			"error":     err.Error(),
		})
		return fmt.Errorf("检查已存在记录失败: %w", err)
	}

	gf.Log().Info(gfctx, "已存在记录检查完成", gf.Map{
		"user_uuid":          userUUID,
		"source_id":          sourceId,
		"total_count":        len(awemeIds),
		"existing_count":     len(existingAwemeIds),
		"existing_aweme_ids": existingAwemeIds,
	})

	// 过滤出需要创建的新记录
	existingSet := make(map[string]bool)
	for _, id := range existingAwemeIds {
		existingSet[id] = true
	}

	var newVideoItems []VideoItemWithTime
	for _, item := range videoItems {
		if !existingSet[item.AwemeID] {
			newVideoItems = append(newVideoItems, item)
		}
	}

	if len(newVideoItems) == 0 {
		gf.Log().Info(gfctx, "所有记录都已存在，跳过创建", gf.Map{
			"user_uuid": userUUID,
			"source_id": sourceId,
		})
		return nil
	}

	gf.Log().Info(gfctx, "准备创建新记录", gf.Map{
		"user_uuid":      userUUID,
		"source_id":      sourceId,
		"new_count":      len(newVideoItems),
		"existing_count": len(existingAwemeIds),
		"total_count":    len(videoItems),
	})

	// 构建记录列表
	var records []*entity.UserInboxVideoRelated
	now := time.Now()

	for _, item := range newVideoItems {
		record := &entity.UserInboxVideoRelated{
			UserUUID:     userUUID,
			SourceId:     sourceId,
			AwemeId:      item.AwemeID,
			SourceType:   entity.SourceTypeCollect, // 收藏夹同步标记为收藏夹类型
			HandleStatus: cons.Pending.Value,       // 默认为待处理状态
		}

		// 设置发布时间：优先使用视频的创建时间，否则使用当前时间
		if item.CreateTime != nil && *item.CreateTime > 0 {
			publishTime := time.Unix(*item.CreateTime, 0)
			record.PublishTime = publishTime
			gf.Log().Debug(gfctx, "使用视频创建时间", gf.Map{
				"aweme_id":     item.AwemeID,
				"create_time":  *item.CreateTime,
				"publish_time": publishTime.Format("2006-01-02 15:04:05"),
			})
		} else {
			record.PublishTime = now
			gf.Log().Debug(gfctx, "使用当前时间作为发布时间", gf.Map{
				"aweme_id":     item.AwemeID,
				"publish_time": now.Format("2006-01-02 15:04:05"),
			})
		}

		// 执行创建前钩子
		if err := record.BeforeCreate(); err != nil {
			gf.Log().Error(gfctx, "记录创建前钩子失败", gf.Map{
				"user_uuid": userUUID,
				"source_id": sourceId,
				"aweme_id":  item.AwemeID,
				"error":     err.Error(),
			})
			return fmt.Errorf("记录 %s 创建前钩子失败: %w", item.AwemeID, err)
		}

		records = append(records, record)
	}

	gf.Log().Info(gfctx, "记录构建完成，开始批量插入", gf.Map{
		"user_uuid":     userUUID,
		"source_id":     sourceId,
		"records_count": len(records),
	})

	// 批量插入数据库
	if err := s.batchInsert(records); err != nil {
		gf.Log().Error(gfctx, "批量插入失败", gf.Map{
			"user_uuid":     userUUID,
			"source_id":     sourceId,
			"records_count": len(records),
			"error":         err.Error(),
		})
		return fmt.Errorf("批量插入失败: %w", err)
	}

	gf.Log().Info(gfctx, "批量创建视频关联记录成功（带创建时间）", gf.Map{
		"user_uuid":      userUUID,
		"source_id":      sourceId,
		"created_count":  len(records),
		"existing_count": len(existingAwemeIds),
		"total_count":    len(videoItems),
	})

	return nil
}

// BatchCreateFromAuthorSync 从作者同步批量创建视频记录
// awemeIds: 抖音视频ID列表
// userUUID: 用户UUID
// sourceId: 来源ID（通常是作者关键词ID）
func (s *UserInboxVideoRelatedService) BatchCreateFromAuthorSync(userUUID, sourceId string, awemeIds []string) error {
	var gfctx = gctx.New()

	gf.Log().Info(gfctx, "开始批量创建作者视频关联记录", gf.Map{
		"user_uuid":   userUUID,
		"source_id":   sourceId,
		"aweme_ids":   awemeIds,
		"aweme_count": len(awemeIds),
	})

	if len(awemeIds) == 0 {
		gf.Log().Info(gfctx, "aweme_ids为空，跳过创建", gf.Map{
			"user_uuid": userUUID,
			"source_id": sourceId,
		})
		return nil
	}

	// 检查已存在的记录，避免重复创建
	existingAwemeIds, err := s.getExistingAwemeIds(userUUID, awemeIds)
	if err != nil {
		gf.Log().Error(gfctx, "检查已存在记录失败", gf.Map{
			"user_uuid": userUUID,
			"source_id": sourceId,
			"aweme_ids": awemeIds,
			"error":     err.Error(),
		})
		return fmt.Errorf("检查已存在记录失败: %w", err)
	}

	gf.Log().Info(gfctx, "检查已存在记录完成", gf.Map{
		"user_uuid":          userUUID,
		"source_id":          sourceId,
		"total_aweme_count":  len(awemeIds),
		"existing_aweme_ids": existingAwemeIds,
		"existing_count":     len(existingAwemeIds),
	})

	// 过滤出需要创建的新记录
	var newAwemeIds []string
	existingMap := make(map[string]bool)
	for _, id := range existingAwemeIds {
		existingMap[id] = true
	}

	for _, awemeId := range awemeIds {
		if !existingMap[awemeId] {
			newAwemeIds = append(newAwemeIds, awemeId)
		}
	}

	gf.Log().Info(gfctx, "过滤新记录完成", gf.Map{
		"user_uuid":      userUUID,
		"source_id":      sourceId,
		"new_aweme_ids":  newAwemeIds,
		"new_count":      len(newAwemeIds),
		"existing_count": len(existingAwemeIds),
	})

	if len(newAwemeIds) == 0 {
		gf.Log().Info(gfctx, "所有记录都已存在，跳过创建", gf.Map{
			"user_uuid":      userUUID,
			"source_id":      sourceId,
			"existing_count": len(existingAwemeIds),
		})
		return nil // 所有记录都已存在
	}

	// 批量创建新记录
	var records []*entity.UserInboxVideoRelated
	now := time.Now()

	gf.Log().Info(gfctx, "开始构建新记录", gf.Map{
		"user_uuid":     userUUID,
		"source_id":     sourceId,
		"new_aweme_ids": newAwemeIds,
		"new_count":     len(newAwemeIds),
	})

	for _, awemeId := range newAwemeIds {
		record := &entity.UserInboxVideoRelated{
			UserUUID:     userUUID,
			SourceId:     sourceId,
			AwemeId:      awemeId,
			SourceType:   entity.SourceTypeAuthor, // 作者同步标记为作者类型
			PublishTime:  now,                     // 默认发布时间，后续可更新
			HandleStatus: cons.Pending.Value,      // 默认为待处理状态
		}

		// 执行创建前钩子
		if err := record.BeforeCreate(); err != nil {
			gf.Log().Error(gfctx, "记录创建前钩子失败", gf.Map{
				"user_uuid": userUUID,
				"source_id": sourceId,
				"aweme_id":  awemeId,
				"error":     err.Error(),
			})
			return fmt.Errorf("记录 %s 创建前钩子失败: %w", awemeId, err)
		}

		records = append(records, record)
	}

	gf.Log().Info(gfctx, "记录构建完成，开始批量插入", gf.Map{
		"user_uuid":     userUUID,
		"source_id":     sourceId,
		"records_count": len(records),
	})

	// 批量插入数据库
	if err := s.batchInsert(records); err != nil {
		gf.Log().Error(gfctx, "批量插入失败", gf.Map{
			"user_uuid":     userUUID,
			"source_id":     sourceId,
			"records_count": len(records),
			"error":         err.Error(),
		})
		return fmt.Errorf("批量插入失败: %w", err)
	}

	gf.Log().Info(gfctx, "批量创建作者视频关联记录成功", gf.Map{
		"user_uuid":      userUUID,
		"source_id":      sourceId,
		"created_count":  len(records),
		"existing_count": len(existingAwemeIds),
		"total_count":    len(awemeIds),
	})

	return nil
}

// BatchCreateFromKeywordSync 从关键词同步批量创建视频记录
// awemeIds: 抖音视频ID列表
// userUUID: 用户UUID
// sourceId: 来源ID（通常是关键词ID）
func (s *UserInboxVideoRelatedService) BatchCreateFromKeywordSync(userUUID, sourceId string, awemeIds []string) error {
	var gfctx = gctx.New()

	gf.Log().Info(gfctx, "开始批量创建关键词视频关联记录", gf.Map{
		"user_uuid":   userUUID,
		"source_id":   sourceId,
		"aweme_ids":   awemeIds,
		"aweme_count": len(awemeIds),
	})

	if len(awemeIds) == 0 {
		gf.Log().Info(gfctx, "aweme_ids为空，跳过创建", gf.Map{
			"user_uuid": userUUID,
			"source_id": sourceId,
		})
		return nil
	}

	// 检查已存在的记录，避免重复创建
	existingAwemeIds, err := s.getExistingAwemeIds(userUUID, awemeIds)
	if err != nil {
		gf.Log().Error(gfctx, "检查已存在记录失败", gf.Map{
			"user_uuid": userUUID,
			"source_id": sourceId,
			"aweme_ids": awemeIds,
			"error":     err.Error(),
		})
		return fmt.Errorf("检查已存在记录失败: %w", err)
	}

	gf.Log().Info(gfctx, "检查已存在记录完成", gf.Map{
		"user_uuid":          userUUID,
		"source_id":          sourceId,
		"total_aweme_count":  len(awemeIds),
		"existing_aweme_ids": existingAwemeIds,
		"existing_count":     len(existingAwemeIds),
	})

	// 过滤出需要创建的新记录
	var newAwemeIds []string
	existingMap := make(map[string]bool)
	for _, id := range existingAwemeIds {
		existingMap[id] = true
	}

	for _, awemeId := range awemeIds {
		if !existingMap[awemeId] {
			newAwemeIds = append(newAwemeIds, awemeId)
		}
	}

	gf.Log().Info(gfctx, "过滤新记录完成", gf.Map{
		"user_uuid":      userUUID,
		"source_id":      sourceId,
		"new_aweme_ids":  newAwemeIds,
		"new_count":      len(newAwemeIds),
		"existing_count": len(existingAwemeIds),
	})

	if len(newAwemeIds) == 0 {
		gf.Log().Info(gfctx, "所有记录都已存在，跳过创建", gf.Map{
			"user_uuid":      userUUID,
			"source_id":      sourceId,
			"existing_count": len(existingAwemeIds),
		})
		return nil // 所有记录都已存在
	}

	// 批量创建新记录
	var records []*entity.UserInboxVideoRelated
	now := time.Now()

	gf.Log().Info(gfctx, "开始构建新记录", gf.Map{
		"user_uuid":     userUUID,
		"source_id":     sourceId,
		"new_aweme_ids": newAwemeIds,
		"new_count":     len(newAwemeIds),
	})

	for _, awemeId := range newAwemeIds {
		record := &entity.UserInboxVideoRelated{
			UserUUID:     userUUID,
			SourceId:     sourceId,
			AwemeId:      awemeId,
			SourceType:   entity.SourceTypeKeyword, // 关键词同步标记为关键词类型
			PublishTime:  now,                      // 默认发布时间，后续可更新
			HandleStatus: cons.Pending.Value,       // 默认为待处理状态
		}

		// 执行创建前钩子
		if err := record.BeforeCreate(); err != nil {
			gf.Log().Error(gfctx, "记录创建前钩子失败", gf.Map{
				"user_uuid": userUUID,
				"source_id": sourceId,
				"aweme_id":  awemeId,
				"error":     err.Error(),
			})
			return fmt.Errorf("记录 %s 创建前钩子失败: %w", awemeId, err)
		}

		records = append(records, record)
	}

	gf.Log().Info(gfctx, "记录构建完成，开始批量插入", gf.Map{
		"user_uuid":     userUUID,
		"source_id":     sourceId,
		"records_count": len(records),
	})

	// 批量插入数据库
	if err := s.batchInsert(records); err != nil {
		gf.Log().Error(gfctx, "批量插入失败", gf.Map{
			"user_uuid":     userUUID,
			"source_id":     sourceId,
			"records_count": len(records),
			"error":         err.Error(),
		})
		return fmt.Errorf("批量插入失败: %w", err)
	}

	gf.Log().Info(gfctx, "批量创建关键词视频关联记录成功", gf.Map{
		"user_uuid":      userUUID,
		"source_id":      sourceId,
		"created_count":  len(records),
		"existing_count": len(existingAwemeIds),
		"total_count":    len(awemeIds),
	})

	return nil
}

// getExistingAwemeIds 获取已存在的抖音视频ID列表
func (s *UserInboxVideoRelatedService) getExistingAwemeIds(userUUID string, awemeIds []string) ([]string, error) {
	var existingRecords []struct {
		AwemeId string `json:"aweme_id"`
	}

	err := gf.Model("user_inbox_video_related").
		Fields("aweme_id").
		Where("user_uuid", userUUID).
		Where("aweme_id IN (?)", awemeIds).
		Where("is_deleted", false).
		Scan(&existingRecords)

	if err != nil {
		return nil, err
	}

	var existingIds []string
	for _, record := range existingRecords {
		existingIds = append(existingIds, record.AwemeId)
	}

	return existingIds, nil
}

// GetExistingAwemeIds 获取已存在的抖音视频ID列表（公开方法）
func (s *UserInboxVideoRelatedService) GetExistingAwemeIds(userUUID string, awemeIds []string) ([]string, error) {
	return s.getExistingAwemeIds(userUUID, awemeIds)
}

// BatchInsert 批量插入记录（公开方法）
func (s *UserInboxVideoRelatedService) BatchInsert(records []*entity.UserInboxVideoRelated) error {
	return s.batchInsert(records)
}

// batchInsert 批量插入记录
func (s *UserInboxVideoRelatedService) batchInsert(records []*entity.UserInboxVideoRelated) error {
	var gfctx = gctx.New()

	if len(records) == 0 {
		gf.Log().Info(gfctx, "批量插入：记录为空，跳过插入", gf.Map{})
		return nil
	}

	gf.Log().Info(gfctx, "开始批量插入记录到数据库", gf.Map{
		"records_count": len(records),
		"table_name":    "user_inbox_video_related",
	})

	// 记录第一条记录的详细信息用于调试
	if len(records) > 0 {
		firstRecord := records[0]
		gf.Log().Info(gfctx, "第一条记录详情", gf.Map{
			"user_uuid":     firstRecord.UserUUID,
			"source_id":     firstRecord.SourceId,
			"aweme_id":      firstRecord.AwemeId,
			"source_type":   firstRecord.SourceType,
			"handle_status": firstRecord.HandleStatus,
		})
	}

	// 使用 INSERT IGNORE 避免唯一键冲突
	result, err := gf.Model("user_inbox_video_related").InsertIgnore(records)
	if err != nil {
		gf.Log().Error(gfctx, "批量插入记录失败", gf.Map{
			"records_count": len(records),
			"error":         err.Error(),
		})
		return fmt.Errorf("批量插入记录失败: %w", err)
	}

	// 记录插入结果
	rowsAffected, _ := result.RowsAffected()
	skippedCount := int64(len(records)) - rowsAffected

	gf.Log().Info(gfctx, "批量插入记录完成", gf.Map{
		"records_count": len(records),
		"rows_affected": rowsAffected,
		"skipped_count": skippedCount,
		"insert_method": "INSERT IGNORE",
	})

	// 如果有跳过的记录，记录详细信息
	if skippedCount > 0 {
		gf.Log().Info(gfctx, "部分记录因重复被跳过", gf.Map{
			"skipped_records": skippedCount,
			"total_records":   len(records),
		})
	}

	return nil
}

// GetByUserAndAwemeId 根据用户和抖音视频ID获取记录
func (s *UserInboxVideoRelatedService) GetByUserAndAwemeId(userUUID, awemeId string) (*entity.UserInboxVideoRelated, error) {
	var record entity.UserInboxVideoRelated
	err := gf.Model("user_inbox_video_related").
		Where("user_uuid", userUUID).
		Where("aweme_id", awemeId).
		Where("is_deleted", false).
		Scan(&record)

	if err != nil {
		return nil, err
	}

	if record.UUID == "" {
		return nil, nil // 未找到记录
	}

	return &record, nil
}

// GetUserVideosByStatus 根据状态获取用户的视频记录
func (s *UserInboxVideoRelatedService) GetUserVideosByStatus(userUUID, status string, page, pageSize int) ([]*entity.UserInboxVideoRelated, int64, error) {
	query := gf.Model("user_inbox_video_related").
		Where("user_uuid", userUUID).
		Where("is_deleted", false)

	if status != "" {
		query = query.Where("handle_status", status)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	var records []*entity.UserInboxVideoRelated
	err = query.
		Order("create_time DESC").
		Limit((page-1)*pageSize, pageSize).
		Scan(&records)

	if err != nil {
		return nil, 0, err
	}

	return records, int64(total), nil
}

// UpdateHandleStatus 更新处理状态
func (s *UserInboxVideoRelatedService) UpdateHandleStatus(uuid, status string) error {
	updateData := map[string]interface{}{
		"handle_status": status,
		"update_time":   time.Now(),
	}

	_, err := gf.Model("user_inbox_video_related").
		Where("uuid", uuid).
		Update(updateData)

	return err
}

// BatchUpdateHandleStatus 批量更新处理状态
func (s *UserInboxVideoRelatedService) BatchUpdateHandleStatus(awemeIds []string, userUUID, status string) error {
	if len(awemeIds) == 0 {
		return nil
	}

	updateData := map[string]interface{}{
		"handle_status": status,
		"update_time":   time.Now(),
	}

	_, err := gf.Model("user_inbox_video_related").
		Where("user_uuid", userUUID).
		Where("aweme_id IN (?)", awemeIds).
		Update(updateData)

	return err
}

// GetPendingRecords 获取待处理的记录
func (s *UserInboxVideoRelatedService) GetPendingRecords(userUUID string, limit int) ([]*entity.UserInboxVideoRelated, error) {
	var records []*entity.UserInboxVideoRelated
	err := gf.Model("user_inbox_video_related").
		Where("user_uuid", userUUID).
		Where("handle_status", cons.Pending.Value).
		Where("is_deleted", false).
		Order("create_time ASC").
		Limit(limit).
		Scan(&records)

	if err != nil {
		return nil, err
	}

	return records, nil
}

// GetVideosBySourceType 根据来源类型获取视频记录
func (s *UserInboxVideoRelatedService) GetVideosBySourceType(userUUID, sourceType string, page, pageSize int) ([]*entity.UserInboxVideoRelated, int64, error) {
	query := gf.Model("user_inbox_video_related").
		Where("user_uuid", userUUID).
		Where("source_type", sourceType).
		Where("is_deleted", false)

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	var records []*entity.UserInboxVideoRelated
	err = query.
		Order("publish_time DESC, create_time DESC").
		Limit((page-1)*pageSize, pageSize).
		Scan(&records)

	if err != nil {
		return nil, 0, err
	}

	return records, int64(total), nil
}

// DeleteByAwemeId 根据抖音视频ID删除记录（软删除）
func (s *UserInboxVideoRelatedService) DeleteByAwemeId(userUUID, awemeId string) error {
	updateData := map[string]interface{}{
		"is_deleted":  true,
		"update_time": time.Now(),
	}

	_, err := gf.Model("user_inbox_video_related").
		Where("user_uuid", userUUID).
		Where("aweme_id", awemeId).
		Update(updateData)

	return err
}

// GetUserVideosWithFilters 根据过滤条件获取用户的视频记录（支持排序）
func (s *UserInboxVideoRelatedService) GetUserVideosWithFilters(userUUID, sourceType, sortBy, sortOrder string, page, pageSize int) ([]*entity.UserInboxVideoRelated, int64, error) {
	query := gf.Model("user_inbox_video_related").
		Where("user_uuid", userUUID).
		Where("is_deleted", false)

	// 添加来源类型过滤
	if sourceType != "" {
		query = query.Where("source_type", sourceType)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		return nil, 0, err
	}

	// 构建排序字段
	var orderBy string
	switch sortBy {
	case "publish_time":
		orderBy = "publish_time"
	case "create_time":
		orderBy = "create_time"
	default:
		orderBy = "create_time" // 默认按创建时间排序
	}

	// 构建排序方向，并添加 UUID 作为第二排序字段确保排序稳定性
	switch sortOrder {
	case "asc":
		orderBy += " ASC, uuid ASC"
	case "desc":
		orderBy += " DESC, uuid DESC"
	default:
		orderBy += " DESC, uuid DESC" // 默认降序
	}

	// 分页查询
	var records []*entity.UserInboxVideoRelated
	err = query.
		Order(orderBy).
		Limit((page-1)*pageSize, pageSize).
		Scan(&records)

	if err != nil {
		return nil, 0, err
	}

	return records, int64(total), nil
}

// GetStatsByUser 获取用户的视频统计信息
func (s *UserInboxVideoRelatedService) GetStatsByUser(userUUID string) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总记录数
	total, err := gf.Model("user_inbox_video_related").
		Where("user_uuid", userUUID).
		Where("is_deleted", false).
		Count()
	if err != nil {
		return nil, err
	}
	stats["total"] = total

	// 按状态统计
	var statusStats []map[string]interface{}
	err = gf.Model("user_inbox_video_related").
		Fields("handle_status, COUNT(*) as count").
		Where("user_uuid", userUUID).
		Where("is_deleted", false).
		Group("handle_status").
		Scan(&statusStats)
	if err != nil {
		return nil, err
	}
	stats["status_stats"] = statusStats

	// 按类型统计
	var typeStats []map[string]interface{}
	err = gf.Model("user_inbox_video_related").
		Fields("source_type, COUNT(*) as count").
		Where("user_uuid", userUUID).
		Where("is_deleted", false).
		Group("source_type").
		Scan(&typeStats)
	if err != nil {
		return nil, err
	}
	stats["type_stats"] = typeStats

	return stats, nil
}

// GetDetailedStatsByUser 获取用户的详细视频统计信息（用于API接口）
func (s *UserInboxVideoRelatedService) GetDetailedStatsByUser(userUUID string) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 1. 获取总记录数（所有来源类型）
	total, err := gf.Model("user_inbox_video_related").
		Where("user_uuid", userUUID).
		Where("is_deleted", false).
		Count()
	if err != nil {
		return nil, fmt.Errorf("查询总记录数失败: %w", err)
	}
	stats["total"] = total

	// 2. 按来源类型分组统计
	type SourceTypeStat struct {
		SourceType string `json:"source_type"`
		Count      int64  `json:"count"`
	}
	var typeStats []SourceTypeStat
	err = gf.Model("user_inbox_video_related").
		Fields("source_type, COUNT(*) as count").
		Where("user_uuid", userUUID).
		Where("is_deleted", false).
		Group("source_type").
		Scan(&typeStats)
	if err != nil {
		return nil, fmt.Errorf("查询来源类型统计失败: %w", err)
	}

	// 转换为更友好的格式，确保所有类型都有值
	sourceTypeStats := map[string]int64{
		string(entity.SourceTypeCollect): 0,
		string(entity.SourceTypeKeyword): 0,
		string(entity.SourceTypeAuthor):  0,
	}
	for _, stat := range typeStats {
		sourceTypeStats[stat.SourceType] = stat.Count
	}
	stats["source_type_stats"] = sourceTypeStats

	// 3. 查询各个类型最新一条数据的创建时间
	type LatestTimeStat struct {
		SourceType string     `json:"source_type"`
		LatestTime *time.Time `json:"latest_time"`
	}
	var latestTimeStats []LatestTimeStat
	err = gf.Model("user_inbox_video_related").
		Fields("source_type, MAX(create_time) as latest_time").
		Where("user_uuid", userUUID).
		Where("is_deleted", false).
		Group("source_type").
		Scan(&latestTimeStats)
	if err != nil {
		return nil, fmt.Errorf("查询最新记录时间失败: %w", err)
	}

	// 转换为更友好的格式，确保所有类型都有值
	latestTimes := map[string]*time.Time{
		string(entity.SourceTypeCollect): nil,
		string(entity.SourceTypeKeyword): nil,
		string(entity.SourceTypeAuthor):  nil,
	}
	for _, stat := range latestTimeStats {
		if stat.LatestTime != nil {
			latestTimes[stat.SourceType] = stat.LatestTime
		}
	}
	stats["latest_times"] = latestTimes

	return stats, nil
}

// GetNewAwemeIds 获取需要新创建关联记录的aweme_id列表
func (s *UserInboxVideoRelatedService) GetNewAwemeIds(userUUID string, awemeIds []string) ([]string, error) {
	// 检查已存在的记录
	existingAwemeIds, err := s.getExistingAwemeIds(userUUID, awemeIds)
	if err != nil {
		return nil, fmt.Errorf("检查已存在记录失败: %w", err)
	}

	// 创建映射以便快速查找
	existingMap := make(map[string]bool)
	for _, id := range existingAwemeIds {
		existingMap[id] = true
	}

	// 过滤出需要创建的新记录
	var newAwemeIds []string
	for _, awemeId := range awemeIds {
		if !existingMap[awemeId] {
			newAwemeIds = append(newAwemeIds, awemeId)
		}
	}

	return newAwemeIds, nil
}

// GetUserVideosWithFiltersAndAweme 根据过滤条件获取用户的视频记录，并关联查询 douyin_aweme 表数据
func (s *UserInboxVideoRelatedService) GetUserVideosWithFiltersAndAweme(userUUID, sourceType, sortBy, sortOrder string, page, pageSize int) ([]*entity.UserInboxVideoRelatedWithAweme, int64, error) {
	var gfctx = gctx.New()

	// 首先获取基础的视频关联记录
	records, total, err := s.GetUserVideosWithFilters(userUUID, sourceType, sortBy, sortOrder, page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询用户视频关联记录失败: %w", err)
	}

	// 如果没有记录，直接返回空结果
	if len(records) == 0 {
		return []*entity.UserInboxVideoRelatedWithAweme{}, total, nil
	}

	// 提取所有的 aweme_id
	awemeIDs := make([]string, len(records))
	for i, record := range records {
		awemeIDs[i] = record.AwemeId
	}

	// 从 media_crawler 数据库查询对应的抖音视频信息
	var douyinAwemes []entity.DouyinAweme
	err = setting.CrawlerModel(entity.DouyinAweme{}).
		Where("aweme_id", awemeIDs).
		Scan(&douyinAwemes)
	if err != nil {
		gf.Log().Error(gfctx, "查询抖音视频信息失败", gf.Map{
			"error":     err.Error(),
			"aweme_ids": awemeIDs,
			"user_uuid": userUUID,
		})
	}

	// 从 media_crawler 数据库查询对应的趋势洞察视频信息（表 id 就是 aweme_id）
	var trendInsightVideos []entity.TrendInsightVideo
	err = setting.CrawlerModel(entity.TrendInsightVideo{}).
		Where("id", awemeIDs).
		Where("is_deleted", false).
		Scan(&trendInsightVideos)
	if err != nil {
		gf.Log().Error(gfctx, "查询趋势洞察视频信息失败", gf.Map{
			"error":     err.Error(),
			"aweme_ids": awemeIDs,
			"user_uuid": userUUID,
		})
	}

	// 如果两个查询都失败，返回基本的关联记录
	if len(douyinAwemes) == 0 && len(trendInsightVideos) == 0 {
		result := make([]*entity.UserInboxVideoRelatedWithAweme, len(records))
		for i, record := range records {
			result[i] = &entity.UserInboxVideoRelatedWithAweme{
				UserInboxVideoRelated: *record,
				DouyinAweme:           nil,
				TrendScore:            nil, // 没有趋势数据时为nil
			}
		}
		return result, total, nil
	}

	// 创建 aweme_id -> DouyinAweme 的映射
	awemeMap := make(map[string]*entity.DouyinAweme)
	for i := range douyinAwemes {
		awemeMap[douyinAwemes[i].AwemeID] = &douyinAwemes[i]
	}

	// 创建 id -> TrendInsightVideo 的映射
	trendInsightMap := make(map[string]*entity.TrendInsightVideo)
	for i := range trendInsightVideos {
		trendInsightMap[trendInsightVideos[i].ID] = &trendInsightVideos[i]
	}

	// 构建最终结果
	result := make([]*entity.UserInboxVideoRelatedWithAweme, len(records))
	for i, record := range records {
		// 计算趋势评分
		var trendScore *float64 = nil
		if trendVideo := trendInsightMap[record.AwemeId]; trendVideo != nil {
			score := float64(int(trendVideo.TrendRadio * trendVideo.TrendScore))
			trendScore = &score
		}

		result[i] = &entity.UserInboxVideoRelatedWithAweme{
			UserInboxVideoRelated: *record,
			DouyinAweme:           awemeMap[record.AwemeId],
			TrendScore:            trendScore,
		}
	}

	gf.Log().Info(gfctx, "查询用户视频关联记录并关联抖音视频成功", gf.Map{
		"user_uuid":                 userUUID,
		"source_type":               sourceType,
		"sort_by":                   sortBy,
		"sort_order":                sortOrder,
		"page":                      page,
		"page_size":                 pageSize,
		"total":                     total,
		"records_count":             len(records),
		"douyin_videos_found":       len(douyinAwemes),
		"trendinsight_videos_found": len(trendInsightVideos),
	})

	return result, total, nil
}

// GetUserVideoStats 获取用户视频统计信息（用于替代 GetSyncStats）
func (s *UserInboxVideoRelatedService) GetUserVideoStats(userUUID string, days int) (*UserVideoStats, error) {
	var gfctx = gctx.New()

	gf.Log().Info(gfctx, "开始获取用户视频统计信息", gf.Map{
		"user_uuid": userUUID,
		"days":      days,
	})

	// 先测试表是否存在，使用简单查询
	gf.Log().Info(gfctx, "测试user_inbox_video_related表连接", gf.Map{
		"user_uuid": userUUID,
	})

	// 测试表是否存在
	testCount, err := gf.Model("user_inbox_video_related").Count()
	if err != nil {
		gf.Log().Error(gfctx, "user_inbox_video_related表不存在或连接失败", gf.Map{
			"error":     err.Error(),
			"user_uuid": userUUID,
		})
		return nil, fmt.Errorf("数据库表不存在或连接失败: %w", err)
	}

	gf.Log().Info(gfctx, "表连接测试成功", gf.Map{
		"user_uuid":   userUUID,
		"table_count": testCount,
	})

	// 1. 查询总记录数（排除软删除）
	gf.Log().Info(gfctx, "开始查询用户总记录数", gf.Map{
		"user_uuid": userUUID,
	})

	totalRecords, err := gf.Model("user_inbox_video_related").
		Where("user_uuid", userUUID).
		Where("is_deleted", false).
		Count()
	if err != nil {
		gf.Log().Error(gfctx, "查询用户总记录数失败", gf.Map{
			"error":     err.Error(),
			"user_uuid": userUUID,
		})
		return nil, fmt.Errorf("查询用户总记录数失败: %w", err)
	}

	gf.Log().Info(gfctx, "查询用户总记录数成功", gf.Map{
		"user_uuid":     userUUID,
		"total_records": totalRecords,
	})

	// 2. 按来源类型分组统计
	type SourceTypeStat struct {
		SourceType string `json:"source_type"`
		Count      int64  `json:"count"`
	}
	var sourceTypeStats []SourceTypeStat
	err = gf.Model("user_inbox_video_related").
		Fields("source_type, COUNT(*) as count").
		Where("user_uuid", userUUID).
		Where("is_deleted", false).
		Group("source_type").
		Scan(&sourceTypeStats)
	if err != nil {
		gf.Log().Error(gfctx, "查询来源类型统计失败", gf.Map{
			"error":     err.Error(),
			"user_uuid": userUUID,
		})
		return nil, fmt.Errorf("查询来源类型统计失败: %w", err)
	}

	// 3. 查询每种类型的最新记录时间
	type LatestTimeStat struct {
		SourceType string     `json:"source_type"`
		LatestTime *time.Time `json:"latest_time"`
	}
	var latestTimeStats []LatestTimeStat
	err = gf.Model("user_inbox_video_related").
		Fields("source_type, MAX(create_time) as latest_time").
		Where("user_uuid", userUUID).
		Where("is_deleted", false).
		Group("source_type").
		Scan(&latestTimeStats)
	if err != nil {
		gf.Log().Error(gfctx, "查询最新记录时间失败", gf.Map{
			"error":     err.Error(),
			"user_uuid": userUUID,
		})
		return nil, fmt.Errorf("查询最新记录时间失败: %w", err)
	}

	// 构建统计结果
	stats := &UserVideoStats{
		Days:         days,
		TotalRecords: int64(totalRecords),
		SourceStats:  make(map[string]int64),
		LatestTimes:  make(map[string]*time.Time),
	}

	// 处理来源类型统计
	for _, stat := range sourceTypeStats {
		stats.SourceStats[stat.SourceType] = stat.Count
	}

	// 处理最新时间统计
	for _, stat := range latestTimeStats {
		if stat.LatestTime != nil {
			stats.LatestTimes[stat.SourceType] = stat.LatestTime
		}
	}

	// 确保所有类型都有统计数据（即使为0）
	sourceTypes := []string{
		string(entity.SourceTypeCollect),
		string(entity.SourceTypeAuthor),
		string(entity.SourceTypeKeyword),
	}
	for _, sourceType := range sourceTypes {
		if _, exists := stats.SourceStats[sourceType]; !exists {
			stats.SourceStats[sourceType] = 0
		}
		if _, exists := stats.LatestTimes[sourceType]; !exists {
			stats.LatestTimes[sourceType] = nil
		}
	}

	gf.Log().Info(gfctx, "获取用户视频统计信息成功", gf.Map{
		"user_uuid":     userUUID,
		"days":          days,
		"total_records": stats.TotalRecords,
		"source_stats":  stats.SourceStats,
	})

	return stats, nil
}

// UserVideoStats 用户视频统计信息
type UserVideoStats struct {
	Days         int                   `json:"days"`          // 统计天数（保留兼容性）
	TotalRecords int64                 `json:"total_records"` // 总记录数
	SourceStats  map[string]int64      `json:"source_stats"`  // 按来源类型统计
	LatestTimes  map[string]*time.Time `json:"latest_times"`  // 每种类型的最新记录时间
}
